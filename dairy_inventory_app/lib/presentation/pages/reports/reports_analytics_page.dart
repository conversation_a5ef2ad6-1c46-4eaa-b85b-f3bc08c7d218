import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../controllers/order_controller.dart';
import '../../controllers/payment_controller.dart';
import '../../controllers/product_controller.dart';

class ReportsAnalyticsPage extends StatefulWidget {
  const ReportsAnalyticsPage({super.key});

  @override
  State<ReportsAnalyticsPage> createState() => _ReportsAnalyticsPageState();
}

class _ReportsAnalyticsPageState extends State<ReportsAnalyticsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final OrderController _orderController = Get.find<OrderController>();
  final PaymentController _paymentController = Get.put(PaymentController());
  final ProductController _productController = Get.put(ProductController());

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports & Analytics'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Sales', icon: Icon(Icons.trending_up)),
            Tab(text: 'Orders', icon: Icon(Icons.shopping_cart)),
            Tab(text: 'Products', icon: Icon(Icons.inventory)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          _SalesReports(),
          _OrderReports(),
          _ProductReports(),
        ],
      ),
    );
  }
}

class _SalesReports extends StatelessWidget {
  const _SalesReports();

  @override
  Widget build(BuildContext context) {
    final PaymentController paymentController = Get.find<PaymentController>();
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      final todaysCollection = paymentController.getTodaysCollection();
      final monthlyCollection = paymentController.getMonthlyCollection();
      final totalRevenue = orderController.getTotalRevenue();
      final pendingAmount = orderController.getPendingAmount();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Cards
            const Text(
              'Sales Overview',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              mainAxisSpacing: AppConstants.spacing16,
              crossAxisSpacing: AppConstants.spacing16,
              children: [
                StatCard(
                  title: 'Today\'s Collection',
                  value: '₹${todaysCollection.toStringAsFixed(0)}',
                  icon: Icons.today,
                  gradient: AppColors.cardGradient1,
                ),
                StatCard(
                  title: 'Monthly Collection',
                  value: '₹${monthlyCollection.toStringAsFixed(0)}',
                  icon: Icons.calendar_month,
                  gradient: AppColors.cardGradient2,
                ),
                StatCard(
                  title: 'Total Revenue',
                  value: '₹${totalRevenue.toStringAsFixed(0)}',
                  icon: Icons.account_balance_wallet,
                  gradient: AppColors.cardGradient3,
                ),
                StatCard(
                  title: 'Pending Amount',
                  value: '₹${pendingAmount.toStringAsFixed(0)}',
                  icon: Icons.pending_actions,
                  gradient: AppColors.cardGradient4,
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing24),

            // Payment Methods Breakdown
            const Text(
              'Payment Methods',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),
            
            _buildPaymentMethodsChart(),

            const SizedBox(height: AppConstants.spacing24),

            // Weekly Revenue Chart
            const Text(
              'Weekly Revenue Trend',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),
            
            _buildWeeklyRevenueChart(),

            const SizedBox(height: AppConstants.spacing24),

            // Export Options
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Export PDF',
                    onPressed: _exportPDF,
                    icon: Icons.picture_as_pdf,
                    type: ButtonType.outline,
                  ),
                ),
                const SizedBox(width: AppConstants.spacing12),
                Expanded(
                  child: CustomButton(
                    text: 'Export Excel',
                    onPressed: _exportExcel,
                    icon: Icons.table_chart,
                    type: ButtonType.outline,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildPaymentMethodsChart() {
    final PaymentController paymentController = Get.find<PaymentController>();
    final paymentsByMethod = paymentController.getPaymentsByMethod();

    return CustomCard(
      child: Column(
        children: paymentsByMethod.entries.map((entry) {
          final method = entry.key;
          final amount = entry.value;
          final total = paymentsByMethod.values.fold(0.0, (sum, val) => sum + val);
          final percentage = total > 0 ? (amount / total) * 100 : 0.0;

          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.spacing12),
            child: Row(
              children: [
                Icon(
                  _getPaymentMethodIcon(method),
                  color: _getPaymentMethodColor(method),
                ),
                const SizedBox(width: AppConstants.spacing12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        method.name.toUpperCase(),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      Container(
                        height: 8,
                        decoration: BoxDecoration(
                          color: AppColors.surfaceVariant,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: percentage / 100,
                          child: Container(
                            decoration: BoxDecoration(
                              color: _getPaymentMethodColor(method),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: AppConstants.spacing12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '₹${amount.toStringAsFixed(0)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      '${percentage.toStringAsFixed(1)}%',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSmall,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildWeeklyRevenueChart() {
    final PaymentController paymentController = Get.find<PaymentController>();
    final weeklyData = paymentController.getDailyCollectionForWeek();

    return CustomCard(
      child: Column(
        children: [
          SizedBox(
            height: 200,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: weeklyData.entries.map((entry) {
                final day = entry.key;
                final amount = entry.value;
                final maxAmount = weeklyData.values.isNotEmpty 
                    ? weeklyData.values.reduce((a, b) => a > b ? a : b)
                    : 1.0;
                final height = maxAmount > 0 ? (amount / maxAmount) * 150 : 0.0;

                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          height: height,
                          decoration: BoxDecoration(
                            gradient: AppColors.primaryGradient,
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(4),
                            ),
                          ),
                        ),
                        const SizedBox(height: AppConstants.spacing8),
                        Text(
                          day,
                          style: const TextStyle(
                            fontSize: AppConstants.fontSmall,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        Text(
                          '₹${amount.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSmall,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getPaymentMethodIcon(method) {
    switch (method.toString()) {
      case 'PaymentMethod.cash':
        return Icons.money;
      case 'PaymentMethod.online':
        return Icons.credit_card;
      case 'PaymentMethod.upi':
        return Icons.qr_code;
      case 'PaymentMethod.cheque':
        return Icons.receipt;
      default:
        return Icons.payment;
    }
  }

  Color _getPaymentMethodColor(method) {
    switch (method.toString()) {
      case 'PaymentMethod.cash':
        return AppColors.success;
      case 'PaymentMethod.online':
        return AppColors.primary;
      case 'PaymentMethod.upi':
        return AppColors.secondary;
      case 'PaymentMethod.cheque':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  void _exportPDF() {
    Get.snackbar(
      'Export PDF',
      'PDF export functionality will be implemented',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _exportExcel() {
    Get.snackbar(
      'Export Excel',
      'Excel export functionality will be implemented',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

class _OrderReports extends StatelessWidget {
  const _OrderReports();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      final statusDistribution = orderController.getOrderStatusDistribution();
      final totalOrders = orderController.orders.length;
      final todaysOrders = orderController.getTodaysOrders().length;
      final pendingOrders = orderController.getPendingOrdersCount();
      final deliveredOrders = orderController.getDeliveredOrdersCount();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Summary
            const Text(
              'Order Overview',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              mainAxisSpacing: AppConstants.spacing16,
              crossAxisSpacing: AppConstants.spacing16,
              children: [
                StatCard(
                  title: 'Total Orders',
                  value: '$totalOrders',
                  icon: Icons.shopping_cart,
                  iconColor: AppColors.primary,
                ),
                StatCard(
                  title: 'Today\'s Orders',
                  value: '$todaysOrders',
                  icon: Icons.today,
                  iconColor: AppColors.secondary,
                ),
                StatCard(
                  title: 'Pending Orders',
                  value: '$pendingOrders',
                  icon: Icons.pending_actions,
                  iconColor: AppColors.warning,
                ),
                StatCard(
                  title: 'Delivered Orders',
                  value: '$deliveredOrders',
                  icon: Icons.check_circle,
                  iconColor: AppColors.success,
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing24),

            // Order Status Distribution
            const Text(
              'Order Status Distribution',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),
            
            CustomCard(
              child: Column(
                children: statusDistribution.entries.map((entry) {
                  final status = entry.key;
                  final count = entry.value;
                  final percentage = totalOrders > 0 ? (count / totalOrders) * 100 : 0.0;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: AppConstants.spacing12),
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getStatusColor(status),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: AppConstants.spacing12),
                        Expanded(
                          child: Text(
                            status.toUpperCase(),
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ),
                        Text(
                          '$count (${percentage.toStringAsFixed(1)}%)',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      );
    });
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppColors.pending;
      case 'confirmed':
        return AppColors.confirmed;
      case 'inprogress':
        return AppColors.inProgress;
      case 'delivered':
        return AppColors.delivered;
      case 'cancelled':
        return AppColors.cancelled;
      default:
        return AppColors.textSecondary;
    }
  }
}

class _ProductReports extends StatelessWidget {
  const _ProductReports();

  @override
  Widget build(BuildContext context) {
    final ProductController productController = Get.find<ProductController>();

    return Obx(() {
      final totalProducts = productController.getProductCount();
      final averagePrice = productController.getAveragePrice();
      final categoryDistribution = productController.getCategoryDistribution();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Summary
            const Text(
              'Product Overview',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),
            
            Row(
              children: [
                Expanded(
                  child: StatCard(
                    title: 'Total Products',
                    value: '$totalProducts',
                    icon: Icons.inventory,
                    iconColor: AppColors.primary,
                  ),
                ),
                const SizedBox(width: AppConstants.spacing16),
                Expanded(
                  child: StatCard(
                    title: 'Average Price',
                    value: '₹${averagePrice.toStringAsFixed(2)}',
                    icon: Icons.attach_money,
                    iconColor: AppColors.secondary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing24),

            // Category Distribution
            const Text(
              'Category Distribution',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),
            
            CustomCard(
              child: Column(
                children: categoryDistribution.entries.map((entry) {
                  final category = entry.key;
                  final count = entry.value;
                  final percentage = totalProducts > 0 ? (count / totalProducts) * 100 : 0.0;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: AppConstants.spacing12),
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getCategoryColor(category),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: AppConstants.spacing12),
                        Expanded(
                          child: Text(
                            category.name.toUpperCase(),
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ),
                        Text(
                          '$count (${percentage.toStringAsFixed(1)}%)',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      );
    });
  }

  Color _getCategoryColor(category) {
    switch (category.toString()) {
      case 'MilkCategory.fullCream':
        return AppColors.primary;
      case 'MilkCategory.toned':
        return AppColors.secondary;
      case 'MilkCategory.doubleToned':
        return AppColors.accent;
      case 'MilkCategory.skimmed':
        return AppColors.success;
      case 'MilkCategory.buffalo':
        return AppColors.warning;
      case 'MilkCategory.organic':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }
}
