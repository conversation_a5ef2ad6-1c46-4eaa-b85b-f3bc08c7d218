import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/notification.dart';
import '../../controllers/notification_controller.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    with SingleTickerProviderStateMixin {
  final NotificationController _notificationController = Get.put(NotificationController());
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(
          'Notifications (${_notificationController.unreadCount})',
        )),
        actions: [
          Obx(() => IconButton(
            onPressed: _notificationController.unreadCount > 0
                ? _notificationController.markAllAsRead
                : null,
            icon: const Icon(Icons.mark_email_read),
            tooltip: 'Mark all as read',
          )),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'toggle',
                child: Row(
                  children: [
                    Obx(() => Icon(
                      _notificationController.notificationsEnabled
                          ? Icons.notifications_off
                          : Icons.notifications,
                    )),
                    const SizedBox(width: 8),
                    Obx(() => Text(
                      _notificationController.notificationsEnabled
                          ? 'Disable Notifications'
                          : 'Enable Notifications',
                    )),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('Clear All'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All', icon: Icon(Icons.notifications)),
            Tab(text: 'Unread', icon: Icon(Icons.mark_email_unread)),
            Tab(text: 'Today', icon: Icon(Icons.today)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          _AllNotifications(),
          _UnreadNotifications(),
          _TodayNotifications(),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'toggle':
        _notificationController.toggleNotifications();
        break;
      case 'clear':
        _showClearConfirmation();
        break;
    }
  }

  void _showClearConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to clear all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _notificationController.clearAllNotifications();
              Get.back();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}

class _AllNotifications extends StatelessWidget {
  const _AllNotifications();

  @override
  Widget build(BuildContext context) {
    final NotificationController notificationController = Get.find<NotificationController>();

    return Obx(() {
      if (notificationController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final notifications = notificationController.notifications;

      if (notifications.isEmpty) {
        return const EmptyState(
          icon: Icons.notifications_none,
          title: 'No Notifications',
          subtitle: 'You\'re all caught up! Notifications will appear here.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return _buildNotificationCard(notification);
        },
      );
    });
  }
}

class _UnreadNotifications extends StatelessWidget {
  const _UnreadNotifications();

  @override
  Widget build(BuildContext context) {
    final NotificationController notificationController = Get.find<NotificationController>();

    return Obx(() {
      final unreadNotifications = notificationController.getUnreadNotifications();

      if (unreadNotifications.isEmpty) {
        return const EmptyState(
          icon: Icons.mark_email_read,
          title: 'No Unread Notifications',
          subtitle: 'All notifications have been read.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: unreadNotifications.length,
        itemBuilder: (context, index) {
          final notification = unreadNotifications[index];
          return _buildNotificationCard(notification);
        },
      );
    });
  }
}

class _TodayNotifications extends StatelessWidget {
  const _TodayNotifications();

  @override
  Widget build(BuildContext context) {
    final NotificationController notificationController = Get.find<NotificationController>();

    return Obx(() {
      final todayNotifications = notificationController.getTodaysNotifications();

      if (todayNotifications.isEmpty) {
        return const EmptyState(
          icon: Icons.today,
          title: 'No Notifications Today',
          subtitle: 'No notifications received today.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: todayNotifications.length,
        itemBuilder: (context, index) {
          final notification = todayNotifications[index];
          return _buildNotificationCard(notification);
        },
      );
    });
  }
}

Widget _buildNotificationCard(AppNotification notification) {
  return CustomCard(
    margin: const EdgeInsets.only(bottom: AppConstants.spacing12),
    color: notification.isRead ? null : AppColors.primary.withValues(alpha: 0.05),
    child: InkWell(
      onTap: () {
        if (!notification.isRead) {
          final NotificationController notificationController = Get.find<NotificationController>();
          notificationController.markAsRead(notification.id);
        }
        _handleNotificationTap(notification);
      },
      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getNotificationColor(notification.type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                  ),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: _getNotificationColor(notification.type),
                    size: AppConstants.iconMedium,
                  ),
                ),
                const SizedBox(width: AppConstants.spacing12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: TextStyle(
                                fontSize: AppConstants.fontLarge,
                                fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: AppColors.primary,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: AppConstants.spacing4),
                      Text(
                        _formatTime(notification.createdAt),
                        style: const TextStyle(
                          fontSize: AppConstants.fontSmall,
                          color: AppColors.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleNotificationAction(value, notification),
                  itemBuilder: (context) => [
                    if (!notification.isRead)
                      const PopupMenuItem(
                        value: 'read',
                        child: Row(
                          children: [
                            Icon(Icons.mark_email_read),
                            SizedBox(width: 8),
                            Text('Mark as Read'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: AppColors.error),
                          SizedBox(width: 8),
                          Text('Delete'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppConstants.spacing12),
            Text(
              notification.body,
              style: TextStyle(
                fontSize: AppConstants.fontMedium,
                color: notification.isRead ? AppColors.textSecondary : AppColors.textPrimary,
              ),
            ),
            if (notification.data.isNotEmpty) ...[
              const SizedBox(height: AppConstants.spacing8),
              _buildNotificationActions(notification),
            ],
          ],
        ),
      ),
    ),
  );
}

Widget _buildNotificationActions(AppNotification notification) {
  switch (notification.type) {
    case NotificationType.orderUpdate:
      return CustomButton(
        text: 'View Order',
        onPressed: () => _navigateToOrder(notification.data['orderId']),
        type: ButtonType.outline,
        icon: Icons.visibility,
      );
    case NotificationType.paymentReceived:
      return Row(
        children: [
          Expanded(
            child: CustomButton(
              text: 'View Payment',
              onPressed: () => _navigateToPayment(notification.data['orderId']),
              type: ButtonType.outline,
              icon: Icons.payment,
            ),
          ),
          const SizedBox(width: AppConstants.spacing8),
          Expanded(
            child: CustomButton(
              text: 'View Order',
              onPressed: () => _navigateToOrder(notification.data['orderId']),
              type: ButtonType.outline,
              icon: Icons.shopping_cart,
            ),
          ),
        ],
      );
    case NotificationType.paymentReminder:
      return CustomButton(
        text: 'View Pending Payments',
        onPressed: () => _navigateToPendingPayments(),
        type: ButtonType.outline,
        icon: Icons.schedule,
      );
    case NotificationType.lowStock:
      return CustomButton(
        text: 'View Inventory',
        onPressed: () => _navigateToInventory(),
        type: ButtonType.outline,
        icon: Icons.inventory,
      );
    case NotificationType.deliveryUpdate:
      return CustomButton(
        text: 'View Delivery',
        onPressed: () => _navigateToDelivery(notification.data['orderId']),
        type: ButtonType.outline,
        icon: Icons.local_shipping,
      );
    case NotificationType.system:
      return const SizedBox.shrink();
  }
}

Color _getNotificationColor(NotificationType type) {
  switch (type) {
    case NotificationType.orderUpdate:
      return AppColors.primary;
    case NotificationType.paymentReceived:
      return AppColors.success;
    case NotificationType.paymentReminder:
      return AppColors.warning;
    case NotificationType.lowStock:
      return AppColors.error;
    case NotificationType.deliveryUpdate:
      return AppColors.secondary;
    case NotificationType.system:
      return AppColors.info;
  }
}

IconData _getNotificationIcon(NotificationType type) {
  switch (type) {
    case NotificationType.orderUpdate:
      return Icons.shopping_cart;
    case NotificationType.paymentReceived:
      return Icons.payment;
    case NotificationType.paymentReminder:
      return Icons.schedule;
    case NotificationType.lowStock:
      return Icons.warning;
    case NotificationType.deliveryUpdate:
      return Icons.local_shipping;
    case NotificationType.system:
      return Icons.info;
  }
}

String _formatTime(DateTime dateTime) {
  final now = DateTime.now();
  final difference = now.difference(dateTime);

  if (difference.inMinutes < 1) {
    return 'Just now';
  } else if (difference.inMinutes < 60) {
    return '${difference.inMinutes}m ago';
  } else if (difference.inHours < 24) {
    return '${difference.inHours}h ago';
  } else if (difference.inDays < 7) {
    return '${difference.inDays}d ago';
  } else {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}

void _handleNotificationTap(AppNotification notification) {
  // Handle notification tap based on type
  switch (notification.type) {
    case NotificationType.orderUpdate:
      _navigateToOrder(notification.data['orderId']);
      break;
    case NotificationType.paymentReceived:
      _navigateToPayment(notification.data['orderId']);
      break;
    case NotificationType.paymentReminder:
      _navigateToPendingPayments();
      break;
    case NotificationType.lowStock:
      _navigateToInventory();
      break;
    case NotificationType.deliveryUpdate:
      _navigateToDelivery(notification.data['orderId']);
      break;
    case NotificationType.system:
      // Handle system notifications
      break;
  }
}

void _handleNotificationAction(String action, AppNotification notification) {
  final NotificationController notificationController = Get.find<NotificationController>();
  
  switch (action) {
    case 'read':
      notificationController.markAsRead(notification.id);
      break;
    case 'delete':
      notificationController.deleteNotification(notification.id);
      break;
  }
}

// Navigation methods
void _navigateToOrder(String? orderId) {
  if (orderId != null) {
    Get.snackbar(
      'Navigation',
      'Navigating to order $orderId',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

void _navigateToPayment(String? orderId) {
  if (orderId != null) {
    Get.snackbar(
      'Navigation',
      'Navigating to payment for order $orderId',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

void _navigateToPendingPayments() {
  Get.snackbar(
    'Navigation',
    'Navigating to pending payments',
    snackPosition: SnackPosition.BOTTOM,
  );
}

void _navigateToInventory() {
  Get.snackbar(
    'Navigation',
    'Navigating to inventory',
    snackPosition: SnackPosition.BOTTOM,
  );
}

void _navigateToDelivery(String? orderId) {
  if (orderId != null) {
    Get.snackbar(
      'Navigation',
      'Navigating to delivery for order $orderId',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
