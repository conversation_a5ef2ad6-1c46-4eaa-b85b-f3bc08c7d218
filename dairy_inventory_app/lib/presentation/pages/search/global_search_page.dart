import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../controllers/search_controller.dart';

class GlobalSearchPage extends StatefulWidget {
  const GlobalSearchPage({super.key});

  @override
  State<GlobalSearchPage> createState() => _GlobalSearchPageState();
}

class _GlobalSearchPageState extends State<GlobalSearchPage>
    with SingleTickerProviderStateMixin {
  final SearchController _searchController = Get.put(SearchController());
  final TextEditingController _searchTextController = TextEditingController();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _searchTextController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Global Search'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(120),
          child: Column(
            children: [
              // Search Bar
              Padding(
                padding: const EdgeInsets.all(AppConstants.spacing16),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _searchTextController,
                        decoration: const InputDecoration(
                          hintText: 'Search orders, customers, products...',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          _searchController.setSearchQuery(value);
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.spacing8),
                    IconButton(
                      onPressed: _showFilterDialog,
                      icon: const Icon(Icons.filter_list),
                      style: IconButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.textOnPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Search Type Tabs
              TabBar(
                controller: _tabController,
                isScrollable: true,
                onTap: (index) {
                  _searchController.setSearchType(SearchType.values[index]);
                },
                tabs: const [
                  Tab(text: 'All', icon: Icon(Icons.search)),
                  Tab(text: 'Orders', icon: Icon(Icons.shopping_cart)),
                  Tab(text: 'Customers', icon: Icon(Icons.people)),
                  Tab(text: 'Products', icon: Icon(Icons.inventory)),
                  Tab(text: 'Payments', icon: Icon(Icons.payment)),
                ],
              ),
            ],
          ),
        ),
      ),
      body: Column(
        children: [
          // Search Results Summary
          _buildSearchSummary(),
          
          // Search Results
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                _AllSearchResults(),
                _OrderSearchResults(),
                _CustomerSearchResults(),
                _ProductSearchResults(),
                _PaymentSearchResults(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSummary() {
    return Obx(() {
      final totalResults = _searchController.getTotalResultsCount();
      final isLoading = _searchController.isLoading;
      
      if (_searchController.searchQuery.isEmpty && 
          _searchController.startDate == null && 
          _searchController.endDate == null) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          border: Border(
            bottom: BorderSide(color: AppColors.shadow, width: 1),
          ),
        ),
        child: Row(
          children: [
            if (isLoading)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              Icon(
                Icons.search_outlined,
                color: AppColors.primary,
                size: AppConstants.iconMedium,
              ),
            const SizedBox(width: AppConstants.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isLoading ? 'Searching...' : '$totalResults results found',
                    style: const TextStyle(
                      fontSize: AppConstants.fontLarge,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (_searchController.searchQuery.isNotEmpty)
                    Text(
                      'for "${_searchController.searchQuery}"',
                      style: const TextStyle(
                        fontSize: AppConstants.fontMedium,
                        color: AppColors.textSecondary,
                      ),
                    ),
                ],
              ),
            ),
            if (_searchController.searchQuery.isNotEmpty || 
                _searchController.startDate != null)
              TextButton(
                onPressed: () {
                  _searchTextController.clear();
                  _searchController.clearSearch();
                },
                child: const Text('Clear'),
              ),
          ],
        ),
      );
    });
  }

  void _showFilterDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Search Filters'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Date Range Filter
            Row(
              children: [
                Expanded(
                  child: Obx(() => TextButton.icon(
                    onPressed: () => _selectDateRange(),
                    icon: const Icon(Icons.date_range),
                    label: Text(
                      _searchController.startDate != null
                          ? 'Date Range Selected'
                          : 'Select Date Range',
                    ),
                  )),
                ),
                if (_searchController.startDate != null)
                  IconButton(
                    onPressed: () => _searchController.clearDateRange(),
                    icon: const Icon(Icons.clear),
                  ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacing16),
            
            // Sort Options
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<SortBy>(
                    value: _searchController.sortBy,
                    decoration: const InputDecoration(
                      labelText: 'Sort By',
                      border: OutlineInputBorder(),
                    ),
                    items: SortBy.values.map((sortBy) {
                      return DropdownMenuItem(
                        value: sortBy,
                        child: Text(sortBy.name.toUpperCase()),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        _searchController.setSortBy(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.spacing8),
                Expanded(
                  child: DropdownButtonFormField<SortOrder>(
                    value: _searchController.sortOrder,
                    decoration: const InputDecoration(
                      labelText: 'Order',
                      border: OutlineInputBorder(),
                    ),
                    items: SortOrder.values.map((order) {
                      return DropdownMenuItem(
                        value: order,
                        child: Text(order.name.toUpperCase()),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        _searchController.setSortOrder(value);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _searchController.startDate != null && _searchController.endDate != null
          ? DateTimeRange(
              start: _searchController.startDate!,
              end: _searchController.endDate!,
            )
          : null,
    );

    if (picked != null) {
      _searchController.setDateRange(picked.start, picked.end);
    }
  }
}

class _AllSearchResults extends StatelessWidget {
  const _AllSearchResults();

  @override
  Widget build(BuildContext context) {
    final SearchController searchController = Get.find<SearchController>();

    return Obx(() {
      if (searchController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final hasResults = searchController.getTotalResultsCount() > 0;
      
      if (!hasResults) {
        return const EmptyState(
          icon: Icons.search_off,
          title: 'No Results Found',
          subtitle: 'Try adjusting your search terms or filters.',
        );
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Orders Section
            if (searchController.orderResults.isNotEmpty) ...[
              _buildSectionHeader('Orders', searchController.orderResults.length),
              ...searchController.orderResults.take(3).map((order) => 
                _buildOrderResultCard(order)),
              if (searchController.orderResults.length > 3)
                _buildViewMoreButton('orders', searchController.orderResults.length - 3),
              const SizedBox(height: AppConstants.spacing24),
            ],
            
            // Customers Section
            if (searchController.customerResults.isNotEmpty) ...[
              _buildSectionHeader('Customers', searchController.customerResults.length),
              ...searchController.customerResults.take(3).map((customer) => 
                _buildCustomerResultCard(customer)),
              if (searchController.customerResults.length > 3)
                _buildViewMoreButton('customers', searchController.customerResults.length - 3),
              const SizedBox(height: AppConstants.spacing24),
            ],
            
            // Products Section
            if (searchController.productResults.isNotEmpty) ...[
              _buildSectionHeader('Products', searchController.productResults.length),
              ...searchController.productResults.take(3).map((product) => 
                _buildProductResultCard(product)),
              if (searchController.productResults.length > 3)
                _buildViewMoreButton('products', searchController.productResults.length - 3),
              const SizedBox(height: AppConstants.spacing24),
            ],
            
            // Payments Section
            if (searchController.paymentResults.isNotEmpty) ...[
              _buildSectionHeader('Payments', searchController.paymentResults.length),
              ...searchController.paymentResults.take(3).map((payment) => 
                _buildPaymentResultCard(payment)),
              if (searchController.paymentResults.length > 3)
                _buildViewMoreButton('payments', searchController.paymentResults.length - 3),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildSectionHeader(String title, int count) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacing12),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: AppConstants.fontXLarge,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(width: AppConstants.spacing8),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.spacing8,
              vertical: AppConstants.spacing4,
            ),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
            ),
            child: Text(
              '$count',
              style: const TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
                fontSize: AppConstants.fontSmall,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewMoreButton(String section, int remainingCount) {
    return Padding(
      padding: const EdgeInsets.only(top: AppConstants.spacing8),
      child: TextButton(
        onPressed: () {
          // Navigate to specific section
        },
        child: Text('View $remainingCount more $section'),
      ),
    );
  }
}

// Individual result card builders
Widget _buildOrderResultCard(order) {
  return CustomCard(
    margin: const EdgeInsets.only(bottom: AppConstants.spacing8),
    child: ListTile(
      leading: const CircleAvatar(
        backgroundColor: AppColors.primary,
        child: Icon(Icons.shopping_cart, color: AppColors.textOnPrimary),
      ),
      title: Text('Order #${order.id.substring(0, 8)}'),
      subtitle: Text('${order.customerName} • ₹${order.totalAmount.toStringAsFixed(2)}'),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getStatusColor(order.status).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          order.status.name.toUpperCase(),
          style: TextStyle(
            color: _getStatusColor(order.status),
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      onTap: () {
        // Navigate to order details
      },
    ),
  );
}

Widget _buildCustomerResultCard(customer) {
  return CustomCard(
    margin: const EdgeInsets.only(bottom: AppConstants.spacing8),
    child: ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.secondary,
        child: Text(
          customer.name.isNotEmpty ? customer.name[0].toUpperCase() : 'C',
          style: const TextStyle(color: AppColors.textOnPrimary),
        ),
      ),
      title: Text(customer.name),
      subtitle: Text('${customer.email} • ${customer.phone}'),
      trailing: Icon(
        customer.isActive ? Icons.check_circle : Icons.cancel,
        color: customer.isActive ? AppColors.success : AppColors.error,
      ),
      onTap: () {
        // Navigate to customer details
      },
    ),
  );
}

Widget _buildProductResultCard(product) {
  return CustomCard(
    margin: const EdgeInsets.only(bottom: AppConstants.spacing8),
    child: ListTile(
      leading: const CircleAvatar(
        backgroundColor: AppColors.accent,
        child: Icon(Icons.local_drink, color: AppColors.textOnPrimary),
      ),
      title: Text(product.name),
      subtitle: Text('${product.category.name} • ${product.fatContent}% Fat'),
      trailing: Text(
        '₹${product.pricePerLiter.toStringAsFixed(2)}/L',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
      onTap: () {
        // Navigate to product details
      },
    ),
  );
}

Widget _buildPaymentResultCard(payment) {
  return CustomCard(
    margin: const EdgeInsets.only(bottom: AppConstants.spacing8),
    child: ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.success,
        child: Icon(
          _getPaymentMethodIcon(payment.method),
          color: AppColors.textOnPrimary,
        ),
      ),
      title: Text(payment.customerName),
      subtitle: Text('${payment.method.name.toUpperCase()} • Order #${payment.orderId.substring(0, 8)}'),
      trailing: Text(
        '₹${payment.amount.toStringAsFixed(2)}',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: AppColors.success,
        ),
      ),
      onTap: () {
        // Navigate to payment details
      },
    ),
  );
}

class _OrderSearchResults extends StatelessWidget {
  const _OrderSearchResults();

  @override
  Widget build(BuildContext context) {
    final SearchController searchController = Get.find<SearchController>();

    return Obx(() {
      if (searchController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final orders = searchController.orderResults;
      
      if (orders.isEmpty) {
        return const EmptyState(
          icon: Icons.shopping_cart_outlined,
          title: 'No Orders Found',
          subtitle: 'Try adjusting your search terms.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return _buildOrderResultCard(order);
        },
      );
    });
  }
}

class _CustomerSearchResults extends StatelessWidget {
  const _CustomerSearchResults();

  @override
  Widget build(BuildContext context) {
    final SearchController searchController = Get.find<SearchController>();

    return Obx(() {
      if (searchController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final customers = searchController.customerResults;
      
      if (customers.isEmpty) {
        return const EmptyState(
          icon: Icons.people_outline,
          title: 'No Customers Found',
          subtitle: 'Try adjusting your search terms.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: customers.length,
        itemBuilder: (context, index) {
          final customer = customers[index];
          return _buildCustomerResultCard(customer);
        },
      );
    });
  }
}

class _ProductSearchResults extends StatelessWidget {
  const _ProductSearchResults();

  @override
  Widget build(BuildContext context) {
    final SearchController searchController = Get.find<SearchController>();

    return Obx(() {
      if (searchController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final products = searchController.productResults;
      
      if (products.isEmpty) {
        return const EmptyState(
          icon: Icons.inventory_2_outlined,
          title: 'No Products Found',
          subtitle: 'Try adjusting your search terms.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return _buildProductResultCard(product);
        },
      );
    });
  }
}

class _PaymentSearchResults extends StatelessWidget {
  const _PaymentSearchResults();

  @override
  Widget build(BuildContext context) {
    final SearchController searchController = Get.find<SearchController>();

    return Obx(() {
      if (searchController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final payments = searchController.paymentResults;
      
      if (payments.isEmpty) {
        return const EmptyState(
          icon: Icons.payment_outlined,
          title: 'No Payments Found',
          subtitle: 'Try adjusting your search terms.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: payments.length,
        itemBuilder: (context, index) {
          final payment = payments[index];
          return _buildPaymentResultCard(payment);
        },
      );
    });
  }
}

// Helper functions
Color _getStatusColor(status) {
  switch (status.toString()) {
    case 'OrderStatus.pending':
      return AppColors.pending;
    case 'OrderStatus.confirmed':
      return AppColors.confirmed;
    case 'OrderStatus.inProgress':
      return AppColors.inProgress;
    case 'OrderStatus.delivered':
      return AppColors.delivered;
    case 'OrderStatus.cancelled':
      return AppColors.cancelled;
    default:
      return AppColors.textSecondary;
  }
}

IconData _getPaymentMethodIcon(method) {
  switch (method.toString()) {
    case 'PaymentMethod.cash':
      return Icons.money;
    case 'PaymentMethod.online':
      return Icons.credit_card;
    case 'PaymentMethod.upi':
      return Icons.qr_code;
    case 'PaymentMethod.cheque':
      return Icons.receipt;
    default:
      return Icons.payment;
  }
}
