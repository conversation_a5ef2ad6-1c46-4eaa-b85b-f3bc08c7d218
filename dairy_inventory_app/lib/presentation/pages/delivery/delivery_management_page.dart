import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/order.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_controller.dart';

class DeliveryManagementPage extends StatefulWidget {
  const DeliveryManagementPage({super.key});

  @override
  State<DeliveryManagementPage> createState() => _DeliveryManagementPageState();
}

class _DeliveryManagementPageState extends State<DeliveryManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delivery Management'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Pending', icon: Icon(Icons.pending_actions)),
            Tab(text: 'In Progress', icon: Icon(Icons.local_shipping)),
            Tab(text: 'Completed', icon: Icon(Icons.check_circle)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          _PendingDeliveries(),
          _InProgressDeliveries(),
          _CompletedDeliveries(),
        ],
      ),
    );
  }
}

class _PendingDeliveries extends StatelessWidget {
  const _PendingDeliveries();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();
    final AuthController authController = Get.find<AuthController>();

    return Obx(() {
      final pendingOrders = orderController.getOrdersByStatus(OrderStatus.confirmed);
      
      if (pendingOrders.isEmpty) {
        return const EmptyState(
          icon: Icons.check_circle_outline,
          title: 'No Pending Deliveries',
          subtitle: 'All deliveries are up to date!',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: pendingOrders.length,
        itemBuilder: (context, index) {
          final order = pendingOrders[index];
          return _buildDeliveryCard(
            order: order,
            onStartDelivery: () => _startDelivery(order),
            onViewDetails: () => _viewOrderDetails(order),
            showStartButton: true,
          );
        },
      );
    });
  }

  void _startDelivery(Order order) {
    Get.dialog(
      AlertDialog(
        title: const Text('Start Delivery'),
        content: Text('Start delivery for order #${order.id.substring(0, 8)}?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final OrderController orderController = Get.find<OrderController>();
              final success = await orderController.updateOrderStatus(order.id, OrderStatus.inProgress);
              if (success) {
                Get.back();
                Get.snackbar(
                  'Delivery Started',
                  'You can now track this delivery in the In Progress tab',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            child: const Text('Start Delivery'),
          ),
        ],
      ),
    );
  }

  void _viewOrderDetails(Order order) {
    // Navigate to order details page
    Get.snackbar(
      'Order Details',
      'Viewing details for order #${order.id.substring(0, 8)}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

class _InProgressDeliveries extends StatelessWidget {
  const _InProgressDeliveries();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      final inProgressOrders = orderController.getOrdersByStatus(OrderStatus.inProgress);
      
      if (inProgressOrders.isEmpty) {
        return const EmptyState(
          icon: Icons.local_shipping_outlined,
          title: 'No Active Deliveries',
          subtitle: 'Start a delivery from the Pending tab.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: inProgressOrders.length,
        itemBuilder: (context, index) {
          final order = inProgressOrders[index];
          return _buildDeliveryCard(
            order: order,
            onCompleteDelivery: () => _completeDelivery(order),
            onViewDetails: () => _viewOrderDetails(order),
            showCompleteButton: true,
          );
        },
      );
    });
  }

  void _completeDelivery(Order order) {
    _showDeliveryConfirmationDialog(order);
  }

  void _showDeliveryConfirmationDialog(Order order) {
    final notesController = TextEditingController();
    bool photoTaken = false;
    bool signatureObtained = false;

    Get.dialog(
      AlertDialog(
        title: const Text('Complete Delivery'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Order #${order.id.substring(0, 8)}'),
                Text('Customer: ${order.customerName}'),
                const SizedBox(height: AppConstants.spacing16),
                
                // Photo Confirmation
                Row(
                  children: [
                    Checkbox(
                      value: photoTaken,
                      onChanged: (value) {
                        setState(() {
                          photoTaken = value ?? false;
                        });
                      },
                    ),
                    const Expanded(
                      child: Text('Photo of delivery taken'),
                    ),
                    IconButton(
                      onPressed: () {
                        _takeDeliveryPhoto();
                        setState(() {
                          photoTaken = true;
                        });
                      },
                      icon: const Icon(Icons.camera_alt),
                    ),
                  ],
                ),
                
                // Signature Confirmation
                Row(
                  children: [
                    Checkbox(
                      value: signatureObtained,
                      onChanged: (value) {
                        setState(() {
                          signatureObtained = value ?? false;
                        });
                      },
                    ),
                    const Expanded(
                      child: Text('Customer signature obtained'),
                    ),
                    IconButton(
                      onPressed: () {
                        _getCustomerSignature();
                        setState(() {
                          signatureObtained = true;
                        });
                      },
                      icon: const Icon(Icons.draw),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.spacing16),
                
                // Delivery Notes
                TextFormField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Delivery Notes (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: photoTaken && signatureObtained
                ? () async {
                    final OrderController orderController = Get.find<OrderController>();
                    final success = await orderController.updateOrderStatus(order.id, OrderStatus.delivered);
                    if (success) {
                      Get.back();
                      Get.snackbar(
                        'Delivery Completed',
                        'Order has been marked as delivered',
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    }
                  }
                : null,
            child: const Text('Complete Delivery'),
          ),
        ],
      ),
    );
  }

  void _takeDeliveryPhoto() {
    Get.snackbar(
      'Photo Capture',
      'Camera functionality will be implemented',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _getCustomerSignature() {
    Get.snackbar(
      'Signature Capture',
      'Signature pad functionality will be implemented',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _viewOrderDetails(Order order) {
    Get.snackbar(
      'Order Details',
      'Viewing details for order #${order.id.substring(0, 8)}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

class _CompletedDeliveries extends StatelessWidget {
  const _CompletedDeliveries();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      final completedOrders = orderController.getOrdersByStatus(OrderStatus.delivered);
      
      if (completedOrders.isEmpty) {
        return const EmptyState(
          icon: Icons.history,
          title: 'No Completed Deliveries',
          subtitle: 'Completed deliveries will appear here.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: completedOrders.length,
        itemBuilder: (context, index) {
          final order = completedOrders[index];
          return _buildDeliveryCard(
            order: order,
            onViewDetails: () => _viewOrderDetails(order),
            isCompleted: true,
          );
        },
      );
    });
  }

  void _viewOrderDetails(Order order) {
    Get.snackbar(
      'Order Details',
      'Viewing details for order #${order.id.substring(0, 8)}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

Widget _buildDeliveryCard({
  required Order order,
  VoidCallback? onStartDelivery,
  VoidCallback? onCompleteDelivery,
  VoidCallback? onViewDetails,
  bool showStartButton = false,
  bool showCompleteButton = false,
  bool isCompleted = false,
}) {
  return CustomCard(
    margin: const EdgeInsets.only(bottom: AppConstants.spacing12),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: _getStatusGradient(order.status),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                _getStatusIcon(order.status),
                color: AppColors.textOnPrimary,
              ),
            ),
            const SizedBox(width: AppConstants.spacing16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order #${order.id.substring(0, 8)}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontMedium,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    order.customerName,
                    style: const TextStyle(
                      fontSize: AppConstants.fontLarge,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    '${order.items.length} items • ₹${order.totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontMedium,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (isCompleted && order.deliveryDate != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                  ),
                  Text(
                    _formatDate(order.deliveryDate!),
                    style: const TextStyle(
                      fontSize: AppConstants.fontSmall,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
          ],
        ),
        
        const SizedBox(height: AppConstants.spacing16),
        
        // Order Items Summary
        Container(
          padding: const EdgeInsets.all(AppConstants.spacing12),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant,
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Items:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: AppConstants.spacing4),
              ...order.items.take(3).map((item) => Text(
                '• ${item.productName} - ${item.quantity.toStringAsFixed(1)}L',
                style: const TextStyle(
                  fontSize: AppConstants.fontSmall,
                  color: AppColors.textSecondary,
                ),
              )),
              if (order.items.length > 3)
                Text(
                  '... and ${order.items.length - 3} more items',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSmall,
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
            ],
          ),
        ),
        
        const SizedBox(height: AppConstants.spacing16),
        
        // Action Buttons
        Row(
          children: [
            if (showStartButton)
              Expanded(
                child: CustomButton(
                  text: 'Start Delivery',
                  onPressed: onStartDelivery,
                  icon: Icons.play_arrow,
                ),
              ),
            
            if (showCompleteButton)
              Expanded(
                child: CustomButton(
                  text: 'Complete Delivery',
                  onPressed: onCompleteDelivery,
                  icon: Icons.check_circle,
                ),
              ),
            
            if (showStartButton || showCompleteButton)
              const SizedBox(width: AppConstants.spacing12),
            
            Expanded(
              child: CustomButton(
                text: 'View Details',
                onPressed: onViewDetails,
                type: ButtonType.outline,
                icon: Icons.visibility,
              ),
            ),
            
            const SizedBox(width: AppConstants.spacing12),
            
            CustomButton(
              text: 'Call',
              onPressed: () {
                // Call customer
              },
              type: ButtonType.outline,
              icon: Icons.phone,
            ),
          ],
        ),
      ],
    ),
  );
}

LinearGradient _getStatusGradient(OrderStatus status) {
  switch (status) {
    case OrderStatus.confirmed:
      return AppColors.cardGradient2;
    case OrderStatus.inProgress:
      return AppColors.cardGradient3;
    case OrderStatus.delivered:
      return AppColors.cardGradient1;
    default:
      return AppColors.cardGradient4;
  }
}

IconData _getStatusIcon(OrderStatus status) {
  switch (status) {
    case OrderStatus.confirmed:
      return Icons.pending_actions;
    case OrderStatus.inProgress:
      return Icons.local_shipping;
    case OrderStatus.delivered:
      return Icons.check_circle;
    default:
      return Icons.help_outline;
  }
}

String _formatDate(DateTime date) {
  return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
}
