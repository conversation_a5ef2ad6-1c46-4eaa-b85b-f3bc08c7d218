import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/user.dart';
import '../../controllers/customer_controller.dart';

class CustomerManagementPage extends StatefulWidget {
  const CustomerManagementPage({super.key});

  @override
  State<CustomerManagementPage> createState() => _CustomerManagementPageState();
}

class _CustomerManagementPageState extends State<CustomerManagementPage> {
  final CustomerController _customerController = Get.put(CustomerController());
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Management'),
        actions: [
          IconButton(
            onPressed: _showAddCustomerDialog,
            icon: const Icon(Icons.person_add),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Section
          _buildSearchSection(),

          // Customer Stats
          _buildCustomerStats(),

          // Customers List
          Expanded(
            child: Obx(() {
              if (_customerController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              final customers = _customerController.getFilteredCustomers();

              if (customers.isEmpty) {
                return const EmptyState(
                  icon: Icons.people_outline,
                  title: 'No Customers Found',
                  subtitle: 'Add your first customer to get started.',
                  buttonText: 'Add Customer',
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(AppConstants.spacing16),
                itemCount: customers.length,
                itemBuilder: (context, index) {
                  final customer = customers[index];
                  return _buildCustomerCard(customer);
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCustomerDialog,
        child: const Icon(Icons.person_add),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacing16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: 'Search customers by name, email, or phone...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: (value) {
          _customerController.setSearchQuery(value);
        },
      ),
    );
  }

  Widget _buildCustomerStats() {
    return Obx(() {
      final totalCustomers = _customerController.getTotalCustomersCount();
      final activeCustomers = _customerController.getActiveCustomersCount();
      final newThisMonth =
          _customerController.getNewCustomersThisMonth().length;

      return Container(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Customers',
                '$totalCustomers',
                Icons.people,
                AppColors.primary,
              ),
            ),
            const SizedBox(width: AppConstants.spacing12),
            Expanded(
              child: _buildStatCard(
                'Active',
                '$activeCustomers',
                Icons.person,
                AppColors.success,
              ),
            ),
            const SizedBox(width: AppConstants.spacing12),
            Expanded(
              child: _buildStatCard(
                'New This Month',
                '$newThisMonth',
                Icons.person_add,
                AppColors.secondary,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacing12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppConstants.iconLarge),
          const SizedBox(height: AppConstants.spacing8),
          Text(
            value,
            style: TextStyle(
              fontSize: AppConstants.fontXLarge,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: AppConstants.fontSmall,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerCard(User customer) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.spacing12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                child: Text(
                  customer.name.isNotEmpty
                      ? customer.name[0].toUpperCase()
                      : 'C',
                  style: const TextStyle(
                    fontSize: AppConstants.fontXLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      customer.name,
                      style: const TextStyle(
                        fontSize: AppConstants.fontLarge,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacing4),
                    Text(
                      customer.email,
                      style: const TextStyle(
                        fontSize: AppConstants.fontMedium,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacing4),
                    Text(
                      customer.phone,
                      style: const TextStyle(
                        fontSize: AppConstants.fontMedium,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleCustomerAction(value, customer),
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'view',
                        child: Row(
                          children: [
                            Icon(Icons.visibility),
                            SizedBox(width: 8),
                            Text('View Details'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'orders',
                        child: Row(
                          children: [
                            Icon(Icons.shopping_cart),
                            SizedBox(width: 8),
                            Text('View Orders'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: AppColors.error),
                            SizedBox(width: 8),
                            Text('Delete'),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),
          const SizedBox(height: AppConstants.spacing12),
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: AppConstants.iconSmall,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: AppConstants.spacing4),
              Expanded(
                child: Text(
                  customer.address ?? 'No address',
                  style: const TextStyle(
                    fontSize: AppConstants.fontMedium,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.spacing8),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.spacing8,
                  vertical: AppConstants.spacing4,
                ),
                decoration: BoxDecoration(
                  color:
                      customer.isActive
                          ? AppColors.success.withValues(alpha: 0.1)
                          : AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                ),
                child: Text(
                  customer.isActive ? 'ACTIVE' : 'INACTIVE',
                  style: TextStyle(
                    color:
                        customer.isActive ? AppColors.success : AppColors.error,
                    fontWeight: FontWeight.bold,
                    fontSize: AppConstants.fontSmall,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                'Joined: ${_formatDate(customer.createdAt)}',
                style: const TextStyle(
                  fontSize: AppConstants.fontSmall,
                  color: AppColors.textTertiary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleCustomerAction(String action, User customer) {
    switch (action) {
      case 'view':
        _showCustomerDetails(customer);
        break;
      case 'edit':
        _showEditCustomerDialog(customer);
        break;
      case 'orders':
        _viewCustomerOrders(customer);
        break;
      case 'delete':
        _showDeleteConfirmation(customer);
        break;
    }
  }

  void _showAddCustomerDialog() {
    _showCustomerDialog();
  }

  void _showEditCustomerDialog(User customer) {
    _showCustomerDialog(customer: customer);
  }

  void _showCustomerDialog({User? customer}) {
    final isEditing = customer != null;
    final nameController = TextEditingController(text: customer?.name ?? '');
    final emailController = TextEditingController(text: customer?.email ?? '');
    final phoneController = TextEditingController(text: customer?.phone ?? '');
    final addressController = TextEditingController(
      text: customer?.address ?? '',
    );

    Get.dialog(
      AlertDialog(
        title: Text(isEditing ? 'Edit Customer' : 'Add New Customer'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Full Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppConstants.spacing16),
              TextFormField(
                controller: emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'Email Address',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppConstants.spacing16),
              TextFormField(
                controller: phoneController,
                keyboardType: TextInputType.phone,
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppConstants.spacing16),
              TextFormField(
                controller: addressController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Address',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty ||
                  emailController.text.trim().isEmpty ||
                  phoneController.text.trim().isEmpty ||
                  addressController.text.trim().isEmpty) {
                Get.snackbar(
                  'Validation Error',
                  'Please fill all fields',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              if (!GetUtils.isEmail(emailController.text.trim())) {
                Get.snackbar(
                  'Validation Error',
                  'Please enter a valid email address',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              final newCustomer = User(
                id:
                    customer?.id ??
                    DateTime.now().millisecondsSinceEpoch.toString(),
                name: nameController.text.trim(),
                email: emailController.text.trim(),
                phone: phoneController.text.trim(),
                address: addressController.text.trim(),
                role: UserRole.customer,
                isActive: customer?.isActive ?? true,
                createdAt: customer?.createdAt ?? DateTime.now(),
              );

              bool success;
              if (isEditing) {
                success = await _customerController.updateCustomer(newCustomer);
              } else {
                success = await _customerController.addCustomer(newCustomer);
              }

              if (success) {
                Get.back();
              }
            },
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  void _showCustomerDetails(User customer) {
    Get.dialog(
      AlertDialog(
        title: Text('Customer Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Name', customer.name),
            _buildDetailRow('Email', customer.email),
            _buildDetailRow('Phone', customer.phone),
            _buildDetailRow('Address', customer.address ?? 'No address'),
            _buildDetailRow(
              'Status',
              customer.isActive ? 'Active' : 'Inactive',
            ),
            _buildDetailRow('Joined', _formatDate(customer.createdAt)),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Close')),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacing8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: AppColors.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  void _viewCustomerOrders(User customer) {
    Get.snackbar(
      'Customer Orders',
      'Viewing orders for ${customer.name}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _showDeleteConfirmation(User customer) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Customer'),
        content: Text('Are you sure you want to delete "${customer.name}"?'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () async {
              final success = await _customerController.deleteCustomer(
                customer.id,
              );
              if (success) {
                Get.back();
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
