import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/order.dart';
import '../../../models/analytics.dart';
import '../../controllers/admin_order_controller.dart';
import 'order_details_page.dart';
import 'create_order_page.dart';
import 'admin_create_order_page.dart';
import 'admin_edit_order_page.dart';
import 'order_assignment_page.dart';

class OrderManagementPage extends StatefulWidget {
  const OrderManagementPage({super.key});

  @override
  State<OrderManagementPage> createState() => _OrderManagementPageState();
}

class _OrderManagementPageState extends State<OrderManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final AdminOrderController _orderController = Get.put(AdminOrderController());
  final List<String> _selectedOrderIds = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
      animationDuration: Duration.zero,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Management'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.textOnPrimary,
          labelColor: AppColors.textOnPrimary,
          unselectedLabelColor: AppColors.textOnPrimary.withOpacity(0.7),
          tabs: const [
            Tab(text: 'All Orders'),
            Tab(text: 'Pending'),
            Tab(text: 'Assigned'),
            Tab(text: 'Analytics'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => Get.to(() => const CreateOrderPage()),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        Icon(Icons.download),
                        SizedBox(width: 8),
                        Text('Export Data'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'bulk_assign',
                    child: Row(
                      children: [
                        Icon(Icons.assignment_ind),
                        SizedBox(width: 8),
                        Text('Bulk Assign'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'bulk_status',
                    child: Row(
                      children: [
                        Icon(Icons.update),
                        SizedBox(width: 8),
                        Text('Bulk Status Update'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllOrdersTab(),
                _buildPendingOrdersTab(),
                _buildAssignedOrdersTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Get.to(() => const AdminCreateOrderPage()),
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: AppColors.textOnPrimary),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Column(
        children: [
          // Search Bar
          TextField(
            onChanged: _orderController.setSearchQuery,
            decoration: InputDecoration(
              hintText: 'Search orders by customer, ID, or notes...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () => _orderController.setSearchQuery(''),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: AppColors.background,
            ),
          ),
          const SizedBox(height: 12),
          // Filter Chips
          Obx(
            () => SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip(
                    'Status: ${_orderController.selectedStatus}',
                    _orderController.selectedStatus != 'all',
                    () => _showStatusFilter(),
                  ),
                  const SizedBox(width: 8),
                  _buildFilterChip(
                    'Priority: ${_orderController.selectedPriority}',
                    _orderController.selectedPriority != 'all',
                    () => _showPriorityFilter(),
                  ),
                  const SizedBox(width: 8),
                  _buildFilterChip(
                    'Category: ${_orderController.selectedCategory}',
                    _orderController.selectedCategory != 'all',
                    () => _showCategoryFilter(),
                  ),
                  const SizedBox(width: 8),
                  _buildFilterChip(
                    'Field Staff: ${_orderController.selectedFieldMan}',
                    _orderController.selectedFieldMan != 'all',
                    () => _showFieldStaffFilter(),
                  ),
                  const SizedBox(width: 8),
                  _buildFilterChip(
                    'Date Range',
                    _orderController.dateRange != null,
                    () => _showDateRangeFilter(),
                  ),
                  const SizedBox(width: 8),
                  if (_hasActiveFilters())
                    TextButton(
                      onPressed: _orderController.clearFilters,
                      child: const Text('Clear All'),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isActive, VoidCallback onTap) {
    return FilterChip(
      label: Text(label),
      selected: isActive,
      onSelected: (_) => onTap(),
      backgroundColor: AppColors.surface,
      selectedColor: AppColors.primary.withOpacity(0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  bool _hasActiveFilters() {
    return _orderController.selectedStatus != 'all' ||
        _orderController.selectedPriority != 'all' ||
        _orderController.selectedCategory != 'all' ||
        _orderController.selectedFieldMan != 'all' ||
        _orderController.dateRange != null;
  }

  Widget _buildAllOrdersTab() {
    return Obx(() {
      if (_orderController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final orders = _orderController.filteredOrders;

      if (orders.isEmpty) {
        return const Center(
          child: EmptyState(
            icon: Icons.shopping_cart_outlined,
            title: 'No Orders Found',
            subtitle: 'No orders match your current filters.',
          ),
        );
      }

      return Column(
        children: [
          _buildOrdersHeader(orders.length),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: orders.length,
              itemBuilder: (context, index) {
                final order = orders[index];
                return _buildOrderCard(order);
              },
            ),
          ),
        ],
      );
    });
  }

  Widget _buildPendingOrdersTab() {
    return Obx(() {
      final pendingOrders = _orderController.getPendingOrders();

      if (pendingOrders.isEmpty) {
        return const Center(
          child: EmptyState(
            icon: Icons.pending_actions,
            title: 'No Pending Orders',
            subtitle: 'All orders have been processed.',
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: pendingOrders.length,
        itemBuilder: (context, index) {
          final order = pendingOrders[index];
          return _buildOrderCard(order, showQuickActions: true);
        },
      );
    });
  }

  Widget _buildAssignedOrdersTab() {
    return Obx(() {
      final assignedOrders =
          _orderController.filteredOrders
              .where((order) => order.fieldManId != null)
              .toList();

      if (assignedOrders.isEmpty) {
        return const Center(
          child: EmptyState(
            icon: Icons.assignment_ind,
            title: 'No Assigned Orders',
            subtitle: 'No orders have been assigned to field staff yet.',
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: assignedOrders.length,
        itemBuilder: (context, index) {
          final order = assignedOrders[index];
          return _buildOrderCard(order, showAssignmentInfo: true);
        },
      );
    });
  }

  Widget _buildAnalyticsTab() {
    return Obx(() {
      final analytics = _orderController.getOrderAnalytics();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAnalyticsOverview(analytics),
            const SizedBox(height: 24),
            _buildCategoryAnalytics(analytics),
            const SizedBox(height: 24),
            _buildTimeSlotAnalytics(analytics),
            const SizedBox(height: 24),
            _buildTopCustomers(analytics),
          ],
        ),
      );
    });
  }

  Widget _buildOrdersHeader(int totalOrders) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppColors.surface,
      child: Row(
        children: [
          Text(
            '$totalOrders Orders',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const Spacer(),
          if (_selectedOrderIds.isNotEmpty) ...[
            Text('${_selectedOrderIds.length} selected'),
            const SizedBox(width: 16),
            TextButton(
              onPressed: _showBulkActions,
              child: const Text('Bulk Actions'),
            ),
          ],
          IconButton(
            icon: Icon(
              _orderController.sortAscending
                  ? Icons.arrow_upward
                  : Icons.arrow_downward,
            ),
            onPressed: _showSortOptions,
          ),
        ],
      ),
    );
  }

  // Placeholder methods for missing functionality
  void _showFilterDialog() {
    // TODO: Implement filter dialog
  }

  void _showStatusFilter() {
    // TODO: Implement status filter
  }

  void _showPriorityFilter() {
    // TODO: Implement priority filter
  }

  void _showCategoryFilter() {
    // TODO: Implement category filter
  }

  void _showFieldStaffFilter() {
    // TODO: Implement field staff filter
  }

  void _showDateRangeFilter() {
    // TODO: Implement date range filter
  }

  void _showSortOptions() {
    // TODO: Implement sort options
  }

  void _showBulkActions() {
    // TODO: Implement bulk actions
  }

  void _handleMenuAction(String action) {
    // TODO: Implement menu actions
  }

  Widget _buildAnalyticsOverview(OrderAnalytics analytics) {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Text('Analytics Overview - Coming Soon'),
      ),
    );
  }

  Widget _buildCategoryAnalytics(OrderAnalytics analytics) {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Text('Category Analytics - Coming Soon'),
      ),
    );
  }

  Widget _buildTimeSlotAnalytics(OrderAnalytics analytics) {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Text('Time Slot Analytics - Coming Soon'),
      ),
    );
  }

  Widget _buildTopCustomers(OrderAnalytics analytics) {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Text('Top Customers - Coming Soon'),
      ),
    );
  }

  Widget _buildOrderCard(
    Order order, {
    bool showQuickActions = false,
    bool showAssignmentInfo = false,
  }) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order #${order.id.length > 8 ? order.id.substring(0, 8) : order.id}',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(order.status).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getStatusDisplayName(order.status),
                  style: TextStyle(
                    color: _getStatusColor(order.status),
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.person, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Text(
                order.customerName,
                style: TextStyle(color: AppColors.textSecondary),
              ),
              const Spacer(),
              Icon(
                Icons.currency_rupee,
                size: 16,
                color: AppColors.textSecondary,
              ),
              Text(
                order.totalAmount.toStringAsFixed(2),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                '${order.orderDate.day}/${order.orderDate.month}/${order.orderDate.year}',
                style: TextStyle(color: AppColors.textSecondary),
              ),
              if (order.deliveryDate != null) ...[
                const SizedBox(width: 16),
                Icon(
                  Icons.local_shipping,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 4),
                Text(
                  '${order.deliveryDate!.day}/${order.deliveryDate!.month}/${order.deliveryDate!.year}',
                  style: TextStyle(color: AppColors.textSecondary),
                ),
              ],
            ],
          ),
          if (showAssignmentInfo && order.fieldManName != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.person_pin, size: 16, color: AppColors.primary),
                const SizedBox(width: 4),
                Text(
                  'Assigned to: ${order.fieldManName}',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'View Details',
                  onPressed: () => Get.to(() => OrderDetailsPage(order: order)),
                  type: ButtonType.outline,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomButton(
                  text: 'Edit',
                  onPressed:
                      () => Get.to(() => AdminEditOrderPage(order: order)),
                ),
              ),
              if (showQuickActions) ...[
                const SizedBox(width: 8),
                Expanded(
                  child: CustomButton(
                    text: 'Assign',
                    onPressed: () => _showAssignmentDialog(order),
                    type: ButtonType.outline,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return AppColors.warning;
      case OrderStatus.confirmed:
        return AppColors.info;
      case OrderStatus.inProgress:
        return AppColors.primary;
      case OrderStatus.delivered:
        return AppColors.success;
      case OrderStatus.cancelled:
        return AppColors.error;
      case OrderStatus.onHold:
        return AppColors.textSecondary;
      case OrderStatus.returned:
        return AppColors.error;
    }
  }

  String _getStatusDisplayName(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProgress:
        return 'In Progress';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.onHold:
        return 'On Hold';
      case OrderStatus.returned:
        return 'Returned';
    }
  }

  void _showAssignmentDialog(Order order) {
    Get.to(() => OrderAssignmentPage(order: order));
  }
}
