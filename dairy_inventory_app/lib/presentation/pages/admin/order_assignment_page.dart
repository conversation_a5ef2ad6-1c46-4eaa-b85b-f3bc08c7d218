import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/order.dart';
import '../../../models/user.dart';
import '../../controllers/admin_order_controller.dart';

class OrderAssignmentPage extends StatefulWidget {
  final Order order;

  const OrderAssignmentPage({
    super.key,
    required this.order,
  });

  @override
  State<OrderAssignmentPage> createState() => _OrderAssignmentPageState();
}

class _OrderAssignmentPageState extends State<OrderAssignmentPage> {
  final AdminOrderController _orderController = Get.find<AdminOrderController>();
  final _notesController = TextEditingController();
  String? _selectedFieldManId;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Assign Order'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOrderInfo(),
            const SizedBox(height: 24),
            _buildFieldStaffSelection(),
            const SizedBox(height: 24),
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Order Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Text('Order ID: '),
                Text(
                  '#${widget.order.id.substring(0, 8)}',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('Customer: '),
                Text(
                  widget.order.customerName,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('Amount: '),
                Text(
                  '₹${widget.order.totalAmount.toStringAsFixed(2)}',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            if (widget.order.deliveryDate != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Text('Delivery Date: '),
                  Text(
                    '${widget.order.deliveryDate!.day}/${widget.order.deliveryDate!.month}/${widget.order.deliveryDate!.year}',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFieldStaffSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select Field Staff',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final fieldStaff = _orderController.fieldStaff;
              
              if (fieldStaff.isEmpty) {
                return const Center(
                  child: Text('No field staff available'),
                );
              }

              return Column(
                children: fieldStaff.map((staff) => 
                  RadioListTile<String>(
                    title: Text(staff.name),
                    subtitle: Text(staff.email),
                    value: staff.id,
                    groupValue: _selectedFieldManId,
                    onChanged: (value) {
                      setState(() {
                        _selectedFieldManId = value;
                      });
                    },
                    secondary: CircleAvatar(
                      backgroundColor: AppColors.primary.withOpacity(0.1),
                      child: Text(
                        staff.name.substring(0, 1).toUpperCase(),
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ).toList(),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Assignment Notes (Optional)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                hintText: 'Add any special instructions or notes...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Obx(() => CustomButton(
            text: 'Assign Order',
            onPressed: _selectedFieldManId != null ? _assignOrder : null,
            isLoading: _orderController.isLoading,
          )),
        ),
      ],
    );
  }

  void _assignOrder() async {
    if (_selectedFieldManId == null) {
      Get.snackbar(
        'Error',
        'Please select a field staff member',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return;
    }

    final success = await _orderController.assignOrderToFieldMan(
      widget.order.id,
      _selectedFieldManId!,
      notes: _notesController.text.trim().isNotEmpty 
          ? _notesController.text.trim() 
          : null,
    );

    if (success) {
      Get.back();
      Get.snackbar(
        'Success',
        'Order assigned successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );
    }
  }
}
