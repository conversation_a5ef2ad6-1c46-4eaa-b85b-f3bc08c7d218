import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/order.dart';
import '../../../models/milk_product.dart';
import '../../../models/user.dart';
import '../../controllers/admin_order_controller.dart';
import '../../controllers/customer_controller.dart';
import '../../controllers/product_controller.dart';

class AdminCreateOrderPage extends StatefulWidget {
  final String? preSelectedCustomerId;

  const AdminCreateOrderPage({super.key, this.preSelectedCustomerId});

  @override
  State<AdminCreateOrderPage> createState() => _AdminCreateOrderPageState();
}

class _AdminCreateOrderPageState extends State<AdminCreateOrderPage> {
  final AdminOrderController _orderController =
      Get.find<AdminOrderController>();
  final CustomerController _customerController = Get.put(CustomerController());
  final ProductController _productController = Get.put(ProductController());

  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _specialInstructionsController = TextEditingController();

  String? _selectedCustomerId;
  User? _selectedCustomer;
  DateTime? _selectedDeliveryDate;
  final Map<String, double> _selectedProducts = {};
  OrderStatus _orderStatus = OrderStatus.pending;
  PaymentStatus _paymentStatus = PaymentStatus.pending;

  @override
  void initState() {
    super.initState();
    _selectedCustomerId = widget.preSelectedCustomerId;
    _loadInitialData();
  }

  void _loadInitialData() {
    _customerController.loadCustomers();
    _productController.loadProducts();

    if (_selectedCustomerId != null) {
      _selectedCustomer = _customerController.getCustomerById(
        _selectedCustomerId!,
      );
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _specialInstructionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Order for Customer'),
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCustomerSelectionSection(),
              const SizedBox(height: 24),
              _buildProductSelectionSection(),
              const SizedBox(height: 24),
              _buildOrderDetailsSection(),
              const SizedBox(height: 24),
              _buildDeliverySection(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerSelectionSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Customer Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Obx(() {
            if (_customerController.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            final customers = _customerController.getActiveCustomers();

            return DropdownButtonFormField<String>(
              value: _selectedCustomerId,
              decoration: const InputDecoration(
                labelText: 'Select Customer *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              items:
                  customers.map((customer) {
                    return DropdownMenuItem(
                      value: customer.id,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            customer.name,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            customer.email,
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCustomerId = value;
                  _selectedCustomer = customers.firstWhereOrNull(
                    (c) => c.id == value,
                  );
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a customer';
                }
                return null;
              },
            );
          }),
          if (_selectedCustomer != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.person, size: 16, color: AppColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        _selectedCustomer!.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.email,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _selectedCustomer!.email,
                        style: TextStyle(color: AppColors.textSecondary),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.phone,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _selectedCustomer!.phone,
                        style: TextStyle(color: AppColors.textSecondary),
                      ),
                    ],
                  ),
                  if (_selectedCustomer!.address != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 14,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _selectedCustomer!.address!,
                            style: TextStyle(color: AppColors.textSecondary),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductSelectionSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Products',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Obx(() {
            if (_productController.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (_productController.products.isEmpty) {
              return const Center(child: Text('No products available'));
            }

            return Column(
              children:
                  _productController.products.map((product) {
                    final quantity = _selectedProducts[product.id] ?? 0.0;
                    return _buildProductItem(product, quantity);
                  }).toList(),
            );
          }),
          if (_selectedProducts.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Amount:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '₹${_calculateTotalAmount().toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductItem(MilkProduct product, double quantity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.border),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '₹${product.pricePerLiter}/liter',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      'Available',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: AppColors.success),
                    ),
                  ],
                ),
              ),
              Switch(
                value: quantity > 0,
                onChanged: (value) {
                  setState(() {
                    if (value) {
                      _selectedProducts[product.id] = 1.0;
                    } else {
                      _selectedProducts.remove(product.id);
                    }
                  });
                },
              ),
            ],
          ),
          if (quantity > 0) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                IconButton(
                  onPressed: () {
                    setState(() {
                      if (quantity > 0.5) {
                        _selectedProducts[product.id] = quantity - 0.5;
                      } else {
                        _selectedProducts.remove(product.id);
                      }
                    });
                  },
                  icon: const Icon(Icons.remove),
                ),
                Expanded(
                  child: Text(
                    '${quantity.toStringAsFixed(1)} L',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _selectedProducts[product.id] = quantity + 0.5;
                    });
                  },
                  icon: const Icon(Icons.add),
                ),
              ],
            ),
            Text(
              'Total: ₹${(quantity * product.pricePerLiter).toStringAsFixed(2)}',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderDetailsSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Details',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<OrderStatus>(
                  value: _orderStatus,
                  decoration: const InputDecoration(
                    labelText: 'Order Status',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      OrderStatus.values.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Text(_getStatusDisplayName(status)),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _orderStatus = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<PaymentStatus>(
                  value: _paymentStatus,
                  decoration: const InputDecoration(
                    labelText: 'Payment Status',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      PaymentStatus.values.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Text(_getPaymentStatusDisplayName(status)),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _paymentStatus = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'Order Notes',
              hintText: 'Any special notes for this order',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildDeliverySection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Delivery Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          InkWell(
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate:
                    _selectedDeliveryDate ??
                    DateTime.now().add(const Duration(days: 1)),
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 30)),
              );
              if (date != null) {
                setState(() {
                  _selectedDeliveryDate = date;
                });
              }
            },
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'Delivery Date',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              child: Text(
                _selectedDeliveryDate != null
                    ? '${_selectedDeliveryDate!.day}/${_selectedDeliveryDate!.month}/${_selectedDeliveryDate!.year}'
                    : 'Select delivery date',
                style: TextStyle(
                  color:
                      _selectedDeliveryDate != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _specialInstructionsController,
            decoration: const InputDecoration(
              labelText: 'Special Instructions',
              hintText: 'Delivery instructions, gate code, etc.',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'Cancel',
            onPressed: () => Get.back(),
            type: ButtonType.outline,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: 'Create Order',
            onPressed: _createOrder,
            isLoading: _orderController.isLoading,
          ),
        ),
      ],
    );
  }

  String _getStatusDisplayName(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProgress:
        return 'In Progress';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.onHold:
        return 'On Hold';
      case OrderStatus.returned:
        return 'Returned';
    }
  }

  String _getPaymentStatusDisplayName(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.partiallyPaid:
        return 'Partially Paid';
      case PaymentStatus.overdue:
        return 'Overdue';
    }
  }

  double _calculateTotalAmount() {
    double total = 0.0;
    _selectedProducts.forEach((productId, quantity) {
      final product = _productController.getProductById(productId);
      if (product != null) {
        total += product.pricePerLiter * quantity;
      }
    });
    return total;
  }

  void _createOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCustomerId == null) {
      Get.snackbar(
        'Error',
        'Please select a customer',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    if (_selectedProducts.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select at least one product',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final orderItems =
        _selectedProducts.entries.map((entry) {
          final product = _productController.getProductById(entry.key)!;
          return OrderItem(
            productId: product.id,
            productName: product.name,
            productCategory: product.category.name,
            quantity: entry.value,
            unitPrice: product.pricePerLiter,
            totalPrice: product.pricePerLiter * entry.value,
          );
        }).toList();

    final totalAmount = _calculateTotalAmount();
    final notes = [
      if (_notesController.text.trim().isNotEmpty) _notesController.text.trim(),
      if (_specialInstructionsController.text.trim().isNotEmpty)
        'Delivery Instructions: ${_specialInstructionsController.text.trim()}',
    ].join('\n');

    final order = Order(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      customerId: _selectedCustomerId!,
      customerName: _selectedCustomer!.name,
      items: orderItems,
      totalAmount: totalAmount,
      paidAmount: _paymentStatus == PaymentStatus.paid ? totalAmount : 0.0,
      pendingAmount: _paymentStatus == PaymentStatus.paid ? 0.0 : totalAmount,
      status: _orderStatus,
      paymentStatus: _paymentStatus,
      orderDate: DateTime.now(),
      deliveryDate: _selectedDeliveryDate,
      notes: notes.isNotEmpty ? notes : null,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final success = await _orderController.createOrder(order);
    if (success) {
      Get.back();
    }
  }
}
