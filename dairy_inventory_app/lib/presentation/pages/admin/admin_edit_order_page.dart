import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/order.dart';
import '../../../models/milk_product.dart';
import '../../../models/user.dart';
import '../../controllers/admin_order_controller.dart';
import '../../controllers/customer_controller.dart';
import '../../controllers/product_controller.dart';

class AdminEditOrderPage extends StatefulWidget {
  final Order order;

  const AdminEditOrderPage({super.key, required this.order});

  @override
  State<AdminEditOrderPage> createState() => _AdminEditOrderPageState();
}

class _AdminEditOrderPageState extends State<AdminEditOrderPage> {
  final AdminOrderController _orderController =
      Get.find<AdminOrderController>();
  final CustomerController _customerController = Get.put(CustomerController());
  final ProductController _productController = Get.put(ProductController());

  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _specialInstructionsController = TextEditingController();

  late String _selectedCustomerId;
  User? _selectedCustomer;
  DateTime? _selectedDeliveryDate;
  final Map<String, double> _selectedProducts = {};
  late OrderStatus _orderStatus;
  late PaymentStatus _paymentStatus;
  late double _paidAmount;

  bool get _canEditItems =>
      _orderStatus == OrderStatus.pending ||
      _orderStatus == OrderStatus.confirmed;

  @override
  void initState() {
    super.initState();
    _initializeFromOrder();
    _loadInitialData();
  }

  void _initializeFromOrder() {
    _selectedCustomerId = widget.order.customerId;
    _selectedDeliveryDate = widget.order.deliveryDate;
    _orderStatus = widget.order.status;
    _paymentStatus = widget.order.paymentStatus;
    _paidAmount = widget.order.paidAmount;

    // Initialize product quantities
    for (final item in widget.order.items) {
      _selectedProducts[item.productId] = item.quantity;
    }

    // Initialize notes
    if (widget.order.notes != null) {
      final notes = widget.order.notes!.split('\n');
      _notesController.text = notes.first;
      if (notes.length > 1 && notes[1].startsWith('Delivery Instructions:')) {
        _specialInstructionsController.text = notes[1].replaceFirst(
          'Delivery Instructions: ',
          '',
        );
      }
    }
  }

  void _loadInitialData() {
    _customerController.loadCustomers();
    _productController.loadProducts();

    _selectedCustomer = _customerController.getCustomerById(
      _selectedCustomerId,
    );
  }

  @override
  void dispose() {
    _notesController.dispose();
    _specialInstructionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Order #${widget.order.id.substring(0, 8)}'),
        elevation: 0,
        actions: [
          if (_canDeleteOrder())
            IconButton(
              onPressed: _showDeleteConfirmation,
              icon: const Icon(Icons.delete),
              color: AppColors.error,
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOrderInfoCard(),
              const SizedBox(height: 24),
              _buildCustomerInfoCard(),
              const SizedBox(height: 24),
              _buildProductSelectionSection(),
              const SizedBox(height: 24),
              _buildOrderStatusSection(),
              const SizedBox(height: 24),
              _buildPaymentSection(),
              const SizedBox(height: 24),
              _buildDeliverySection(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderInfoCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order Information',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(_orderStatus).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  _getStatusDisplayName(_orderStatus),
                  style: TextStyle(
                    color: _getStatusColor(_orderStatus),
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'Order ID',
                  '#${widget.order.id.substring(0, 8)}',
                  Icons.receipt,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'Order Date',
                  '${widget.order.orderDate.day}/${widget.order.orderDate.month}/${widget.order.orderDate.year}',
                  Icons.calendar_today,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'Total Amount',
                  '₹${widget.order.totalAmount.toStringAsFixed(2)}',
                  Icons.currency_rupee,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'Pending Amount',
                  '₹${widget.order.pendingAmount.toStringAsFixed(2)}',
                  Icons.pending_actions,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
            ),
            Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomerInfoCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Customer Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          if (_selectedCustomer != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.person, size: 16, color: AppColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        _selectedCustomer!.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.email,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _selectedCustomer!.email,
                        style: TextStyle(color: AppColors.textSecondary),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.phone,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _selectedCustomer!.phone,
                        style: TextStyle(color: AppColors.textSecondary),
                      ),
                    ],
                  ),
                  if (_selectedCustomer!.address != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 14,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _selectedCustomer!.address!,
                            style: TextStyle(color: AppColors.textSecondary),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return AppColors.warning;
      case OrderStatus.confirmed:
        return AppColors.info;
      case OrderStatus.inProgress:
        return AppColors.primary;
      case OrderStatus.delivered:
        return AppColors.success;
      case OrderStatus.cancelled:
        return AppColors.error;
      case OrderStatus.onHold:
        return AppColors.textSecondary;
      case OrderStatus.returned:
        return AppColors.error;
    }
  }

  String _getStatusDisplayName(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProgress:
        return 'In Progress';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.onHold:
        return 'On Hold';
      case OrderStatus.returned:
        return 'Returned';
    }
  }

  bool _canDeleteOrder() {
    return _orderStatus == OrderStatus.pending ||
        _orderStatus == OrderStatus.confirmed ||
        _orderStatus == OrderStatus.onHold;
  }

  Widget _buildProductSelectionSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order Items',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              if (!_canEditItems)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Read Only',
                    style: TextStyle(
                      color: AppColors.warning,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          Obx(() {
            if (_productController.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (_productController.products.isEmpty) {
              return const Center(child: Text('No products available'));
            }

            return Column(
              children:
                  _productController.products.map((product) {
                    final quantity = _selectedProducts[product.id] ?? 0.0;
                    return _buildProductItem(product, quantity);
                  }).toList(),
            );
          }),
          if (_selectedProducts.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Amount:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '₹${_calculateTotalAmount().toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductItem(MilkProduct product, double quantity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.border),
        borderRadius: BorderRadius.circular(8),
        color: _canEditItems ? null : AppColors.surface.withValues(alpha: 0.5),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '₹${product.pricePerLiter}/liter',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              if (_canEditItems)
                Switch(
                  value: quantity > 0,
                  onChanged: (value) {
                    setState(() {
                      if (value) {
                        _selectedProducts[product.id] = 1.0;
                      } else {
                        _selectedProducts.remove(product.id);
                      }
                    });
                  },
                ),
            ],
          ),
          if (quantity > 0) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                if (_canEditItems)
                  IconButton(
                    onPressed: () {
                      setState(() {
                        if (quantity > 0.5) {
                          _selectedProducts[product.id] = quantity - 0.5;
                        } else {
                          _selectedProducts.remove(product.id);
                        }
                      });
                    },
                    icon: const Icon(Icons.remove),
                  )
                else
                  const SizedBox(width: 48),
                Expanded(
                  child: Text(
                    '${quantity.toStringAsFixed(1)} L',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                if (_canEditItems)
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _selectedProducts[product.id] = quantity + 0.5;
                      });
                    },
                    icon: const Icon(Icons.add),
                  )
                else
                  const SizedBox(width: 48),
              ],
            ),
            Text(
              'Total: ₹${(quantity * product.pricePerLiter).toStringAsFixed(2)}',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showDeleteConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Order'),
        content: Text(
          'Are you sure you want to delete this order? This action cannot be undone.\n\n'
          'Order: #${widget.order.id.substring(0, 8)}\n'
          'Customer: ${widget.order.customerName}\n'
          'Amount: ₹${widget.order.totalAmount.toStringAsFixed(2)}',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              final success = await _orderController.deleteOrder(
                widget.order.id,
                reason: 'Deleted by admin',
              );
              if (success) {
                Get.back(); // Go back to order management
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatusSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Status',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<OrderStatus>(
            value: _orderStatus,
            decoration: const InputDecoration(
              labelText: 'Status',
              border: OutlineInputBorder(),
            ),
            items:
                OrderStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusDisplayName(status)),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _orderStatus = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'Order Notes',
              hintText: 'Any special notes for this order',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<PaymentStatus>(
                  value: _paymentStatus,
                  decoration: const InputDecoration(
                    labelText: 'Payment Status',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      PaymentStatus.values.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Text(_getPaymentStatusDisplayName(status)),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _paymentStatus = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  initialValue: _paidAmount.toStringAsFixed(2),
                  decoration: const InputDecoration(
                    labelText: 'Paid Amount',
                    border: OutlineInputBorder(),
                    prefixText: '₹',
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _paidAmount = double.tryParse(value) ?? 0.0;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDeliverySection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Delivery Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          InkWell(
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate:
                    _selectedDeliveryDate ??
                    DateTime.now().add(const Duration(days: 1)),
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 30)),
              );
              if (date != null) {
                setState(() {
                  _selectedDeliveryDate = date;
                });
              }
            },
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'Delivery Date',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              child: Text(
                _selectedDeliveryDate != null
                    ? '${_selectedDeliveryDate!.day}/${_selectedDeliveryDate!.month}/${_selectedDeliveryDate!.year}'
                    : 'Select delivery date',
                style: TextStyle(
                  color:
                      _selectedDeliveryDate != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _specialInstructionsController,
            decoration: const InputDecoration(
              labelText: 'Special Instructions',
              hintText: 'Delivery instructions, gate code, etc.',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'Cancel',
            onPressed: () => Get.back(),
            type: ButtonType.outline,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: 'Update Order',
            onPressed: _updateOrder,
            isLoading: _orderController.isLoading,
          ),
        ),
      ],
    );
  }

  String _getPaymentStatusDisplayName(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.partiallyPaid:
        return 'Partially Paid';
      case PaymentStatus.overdue:
        return 'Overdue';
    }
  }

  double _calculateTotalAmount() {
    double total = 0.0;
    _selectedProducts.forEach((productId, quantity) {
      final product = _productController.getProductById(productId);
      if (product != null) {
        total += product.pricePerLiter * quantity;
      }
    });
    return total;
  }

  void _updateOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedProducts.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select at least one product',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final orderItems =
        _selectedProducts.entries.map((entry) {
          final product = _productController.getProductById(entry.key)!;
          return OrderItem(
            productId: product.id,
            productName: product.name,
            productCategory: product.category.name,
            quantity: entry.value,
            unitPrice: product.pricePerLiter,
            totalPrice: product.pricePerLiter * entry.value,
          );
        }).toList();

    final totalAmount = _calculateTotalAmount();
    final pendingAmount = totalAmount - _paidAmount;
    final notes = [
      if (_notesController.text.trim().isNotEmpty) _notesController.text.trim(),
      if (_specialInstructionsController.text.trim().isNotEmpty)
        'Delivery Instructions: ${_specialInstructionsController.text.trim()}',
    ].join('\n');

    final updatedOrder = widget.order.copyWith(
      items: orderItems,
      totalAmount: totalAmount,
      paidAmount: _paidAmount,
      pendingAmount: pendingAmount,
      status: _orderStatus,
      paymentStatus: _paymentStatus,
      deliveryDate: _selectedDeliveryDate,
      notes: notes.isNotEmpty ? notes : null,
      updatedAt: DateTime.now(),
    );

    final success = await _orderController.updateOrder(updatedOrder);
    if (success) {
      Get.back();
    }
  }
}
