import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/order.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_controller.dart';
import '../orders/order_details_page.dart';
import '../products/product_management_page.dart';
import '../reports/reports_analytics_page.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard>
    with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
      animationDuration: Duration.zero, // Remove animation to prevent shaking
    );
    // Defer heavy operations to reduce initial frame load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.microtask(() => _orderController.loadOrders());
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _logout() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await _authController.logout();
        Get.snackbar(
          'Success',
          'Logged out successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: AppColors.textOnPrimary,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to logout. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error,
          colorText: AppColors.textOnPrimary,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppColors.primaryGradient),
        ),
        title: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.surface,
              child: Icon(
                Icons.admin_panel_settings,
                color: AppColors.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Welcome back!',
                    style: TextStyle(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    _authController.currentUser?.name ?? 'Admin',
                    style: const TextStyle(
                      color: AppColors.textOnPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _logout,
            icon: const Icon(Icons.logout, color: AppColors.textOnPrimary),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          _DashboardOverview(),
          _OrdersManagement(),
          _InventoryManagement(),
          _ReportsView(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          onTap: (index) {
            // Single operation to prevent timing conflicts
            setState(() {
              _selectedIndex = index;
              _tabController.index = index;
            });
          },
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3,
          labelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.shopping_cart), text: 'Orders'),
            Tab(icon: Icon(Icons.inventory), text: 'Inventory'),
            Tab(icon: Icon(Icons.analytics), text: 'Reports'),
          ],
        ),
      ),
    );
  }
}

class _DashboardOverview extends StatelessWidget {
  const _DashboardOverview();

  @override
  Widget build(BuildContext context) {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      physics: BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [_StatsGrid(), SizedBox(height: 24), _RecentOrdersSection()],
      ),
    );
  }
}

class _StatsGrid extends StatelessWidget {
  const _StatsGrid();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      if (orderController.isLoading) {
        return const SizedBox(
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        );
      }

      // Optimized single-pass calculation to prevent multiple iterations
      final orders = orderController.orders;
      final today = DateTime.now();

      int todaysOrdersCount = 0;
      double totalRevenue = 0.0;
      double pendingAmount = 0.0;
      int pendingOrdersCount = 0;

      // Single iteration through orders for all calculations
      for (final order in orders) {
        totalRevenue += order.totalAmount;
        pendingAmount += order.pendingAmount;

        if (order.status == OrderStatus.pending) {
          pendingOrdersCount++;
        }

        if (order.orderDate.year == today.year &&
            order.orderDate.month == today.month &&
            order.orderDate.day == today.day) {
          todaysOrdersCount++;
        }
      }

      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Today\'s Orders',
                  value: '$todaysOrdersCount',
                  icon: Icons.today,
                  gradient: AppColors.cardGradient1,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: StatCard(
                  title: 'Total Revenue',
                  value: '₹${totalRevenue.toStringAsFixed(0)}',
                  icon: Icons.trending_up,
                  gradient: AppColors.cardGradient2,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Pending Orders',
                  value: '$pendingOrdersCount',
                  icon: Icons.pending_actions,
                  gradient: AppColors.cardGradient3,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: StatCard(
                  title: 'Pending Amount',
                  value: '₹${pendingAmount.toStringAsFixed(0)}',
                  icon: Icons.payments,
                  gradient: AppColors.cardGradient4,
                ),
              ),
            ],
          ),
        ],
      );
    });
  }
}

class _RecentOrdersSection extends StatelessWidget {
  const _RecentOrdersSection();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Recent Orders',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                // Navigate to orders tab
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.spacing16),
        Obx(() {
          if (orderController.isLoading) {
            return const SizedBox(
              height: 100,
              child: Center(child: CircularProgressIndicator()),
            );
          }

          if (orderController.orders.isEmpty) {
            return const EmptyState(
              icon: Icons.shopping_cart_outlined,
              title: 'No Orders Yet',
              subtitle:
                  'Orders will appear here once customers start placing them.',
            );
          }

          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: orderController.orders.take(5).length,
            itemBuilder: (context, index) {
              final order = orderController.orders[index];
              return OrderCard(
                orderId: order.id,
                customerName: order.customerName,
                amount: '₹${order.totalAmount.toStringAsFixed(2)}',
                status: order.status.name,
                date:
                    '${order.orderDate.day}/${order.orderDate.month}/${order.orderDate.year}',
                statusColor: _getStatusColor(order.status),
                onTap: () {
                  Get.to(() => OrderDetailsPage(order: order));
                },
              );
            },
          );
        }),
      ],
    );
  }

  Color _getStatusColor(status) {
    switch (status.toString()) {
      case 'OrderStatus.pending':
        return AppColors.pending;
      case 'OrderStatus.confirmed':
        return AppColors.confirmed;
      case 'OrderStatus.delivered':
        return AppColors.delivered;
      case 'OrderStatus.cancelled':
        return AppColors.cancelled;
      default:
        return AppColors.textSecondary;
    }
  }
}

class _OrdersManagement extends StatelessWidget {
  const _OrdersManagement();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Orders Management',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}

class _InventoryManagement extends StatelessWidget {
  const _InventoryManagement();

  @override
  Widget build(BuildContext context) {
    return ProductManagementPage();
  }
}

class _ReportsView extends StatelessWidget {
  const _ReportsView();

  @override
  Widget build(BuildContext context) {
    return const ReportsAnalyticsPage();
  }
}
