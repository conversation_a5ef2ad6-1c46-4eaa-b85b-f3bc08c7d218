import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_controller.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard>
    with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _orderController.loadOrders();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _logout() async {
    await _authController.logout();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: AppColors.surface,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: AppColors.primaryGradient,
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConstants.spacing16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 25,
                                backgroundColor: AppColors.surface,
                                child: Icon(
                                  Icons.admin_panel_settings,
                                  color: AppColors.primary,
                                  size: AppConstants.iconLarge,
                                ),
                              ),
                              const SizedBox(width: AppConstants.spacing16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Welcome back!',
                                      style: TextStyle(
                                        color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                                        fontSize: AppConstants.fontMedium,
                                      ),
                                    ),
                                    Text(
                                      _authController.currentUser?.name ?? 'Admin',
                                      style: const TextStyle(
                                        color: AppColors.textOnPrimary,
                                        fontSize: AppConstants.fontXLarge,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              IconButton(
                                onPressed: _logout,
                                icon: const Icon(
                                  Icons.logout,
                                  color: AppColors.textOnPrimary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: const [
            _DashboardOverview(),
            _OrdersManagement(),
            _InventoryManagement(),
            _ReportsView(),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          onTap: (index) => setState(() => _selectedIndex = index),
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3,
          tabs: const [
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'Dashboard',
            ),
            Tab(
              icon: Icon(Icons.shopping_cart),
              text: 'Orders',
            ),
            Tab(
              icon: Icon(Icons.inventory),
              text: 'Inventory',
            ),
            Tab(
              icon: Icon(Icons.analytics),
              text: 'Reports',
            ),
          ],
        ),
      ),
    );
  }
}

class _DashboardOverview extends StatelessWidget {
  const _DashboardOverview();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      if (orderController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final todaysOrders = orderController.getTodaysOrders();
      final totalRevenue = orderController.getTotalRevenue();
      final pendingAmount = orderController.getPendingAmount();
      final pendingOrders = orderController.getPendingOrdersCount();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Stats Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              mainAxisSpacing: AppConstants.spacing16,
              crossAxisSpacing: AppConstants.spacing16,
              children: [
                StatCard(
                  title: 'Today\'s Orders',
                  value: '${todaysOrders.length}',
                  icon: Icons.today,
                  gradient: AppColors.cardGradient1,
                ),
                StatCard(
                  title: 'Total Revenue',
                  value: '₹${totalRevenue.toStringAsFixed(0)}',
                  icon: Icons.trending_up,
                  gradient: AppColors.cardGradient2,
                ),
                StatCard(
                  title: 'Pending Orders',
                  value: '$pendingOrders',
                  icon: Icons.pending_actions,
                  gradient: AppColors.cardGradient3,
                ),
                StatCard(
                  title: 'Pending Amount',
                  value: '₹${pendingAmount.toStringAsFixed(0)}',
                  icon: Icons.payments,
                  gradient: AppColors.cardGradient4,
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing24),

            // Recent Orders Section
            Row(
              children: [
                const Text(
                  'Recent Orders',
                  style: TextStyle(
                    fontSize: AppConstants.fontXLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to orders tab
                  },
                  child: const Text('View All'),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing16),

            // Orders List
            if (orderController.orders.isEmpty)
              const EmptyState(
                icon: Icons.shopping_cart_outlined,
                title: 'No Orders Yet',
                subtitle: 'Orders will appear here once customers start placing them.',
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: orderController.orders.take(5).length,
                itemBuilder: (context, index) {
                  final order = orderController.orders[index];
                  return OrderCard(
                    orderId: order.id,
                    customerName: order.customerName,
                    amount: '₹${order.totalAmount.toStringAsFixed(2)}',
                    status: order.status.name,
                    date: '${order.orderDate.day}/${order.orderDate.month}/${order.orderDate.year}',
                    statusColor: _getStatusColor(order.status),
                    onTap: () {
                      // Navigate to order details
                    },
                  );
                },
              ),
          ],
        ),
      );
    });
  }

  Color _getStatusColor(status) {
    switch (status.toString()) {
      case 'OrderStatus.pending':
        return AppColors.pending;
      case 'OrderStatus.confirmed':
        return AppColors.confirmed;
      case 'OrderStatus.delivered':
        return AppColors.delivered;
      case 'OrderStatus.cancelled':
        return AppColors.cancelled;
      default:
        return AppColors.textSecondary;
    }
  }
}

class _OrdersManagement extends StatelessWidget {
  const _OrdersManagement();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Orders Management',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}

class _InventoryManagement extends StatelessWidget {
  const _InventoryManagement();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Inventory Management',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}

class _ReportsView extends StatelessWidget {
  const _ReportsView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Reports & Analytics',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}
