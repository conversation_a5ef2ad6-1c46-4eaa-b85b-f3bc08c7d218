import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_controller.dart';
import '../orders/order_details_page.dart';
import '../products/product_management_page.dart';
import '../reports/reports_analytics_page.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard>
    with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _selectedIndex = _tabController.index;
        });
      }
    });
    // Defer heavy operations to reduce initial frame load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.microtask(() => _orderController.loadOrders());
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _logout() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await _authController.logout();
        Get.snackbar(
          'Success',
          'Logged out successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: AppColors.textOnPrimary,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to logout. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error,
          colorText: AppColors.textOnPrimary,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: AppColors.surface,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: AppColors.primaryGradient,
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConstants.spacing16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 25,
                                backgroundColor: AppColors.surface,
                                child: Icon(
                                  Icons.admin_panel_settings,
                                  color: AppColors.primary,
                                  size: AppConstants.iconLarge,
                                ),
                              ),
                              const SizedBox(width: AppConstants.spacing16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Welcome back!',
                                      style: TextStyle(
                                        color: AppColors.textOnPrimary
                                            .withValues(alpha: 0.8),
                                        fontSize: AppConstants.fontMedium,
                                      ),
                                    ),
                                    Text(
                                      _authController.currentUser?.name ??
                                          'Admin',
                                      style: const TextStyle(
                                        color: AppColors.textOnPrimary,
                                        fontSize: AppConstants.fontXLarge,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              IconButton(
                                onPressed: _logout,
                                icon: const Icon(
                                  Icons.logout,
                                  color: AppColors.textOnPrimary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ];
        },
        body: RepaintBoundary(
          child: TabBarView(
            controller: _tabController,
            children: const [
              RepaintBoundary(child: _DashboardOverview()),
              RepaintBoundary(child: _OrdersManagement()),
              RepaintBoundary(child: _InventoryManagement()),
              RepaintBoundary(child: _ReportsView()),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });
            _tabController.animateTo(index);
          },
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3,
          labelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.shopping_cart), text: 'Orders'),
            Tab(icon: Icon(Icons.inventory), text: 'Inventory'),
            Tab(icon: Icon(Icons.analytics), text: 'Reports'),
          ],
        ),
      ),
    );
  }
}

class _DashboardOverview extends StatelessWidget {
  const _DashboardOverview();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      if (orderController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final todaysOrders = orderController.getTodaysOrders();
      final totalRevenue = orderController.getTotalRevenue();
      final pendingAmount = orderController.getPendingAmount();
      final pendingOrders = orderController.getPendingOrdersCount();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Stats Grid - Simple Layout
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: StatCard(
                        title: 'Today\'s Orders',
                        value: '${todaysOrders.length}',
                        icon: Icons.today,
                        gradient: AppColors.cardGradient1,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: StatCard(
                        title: 'Total Revenue',
                        value: '₹${totalRevenue.toStringAsFixed(0)}',
                        icon: Icons.trending_up,
                        gradient: AppColors.cardGradient2,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: StatCard(
                        title: 'Pending Orders',
                        value: '$pendingOrders',
                        icon: Icons.pending_actions,
                        gradient: AppColors.cardGradient3,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: StatCard(
                        title: 'Pending Amount',
                        value: '₹${pendingAmount.toStringAsFixed(0)}',
                        icon: Icons.payments,
                        gradient: AppColors.cardGradient4,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing24),

            // Recent Orders Section
            Row(
              children: [
                const Text(
                  'Recent Orders',
                  style: TextStyle(
                    fontSize: AppConstants.fontXLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to orders tab
                  },
                  child: const Text('View All'),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing16),

            // Orders List
            if (orderController.orders.isEmpty)
              const EmptyState(
                icon: Icons.shopping_cart_outlined,
                title: 'No Orders Yet',
                subtitle:
                    'Orders will appear here once customers start placing them.',
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: orderController.orders.take(5).length,
                itemBuilder: (context, index) {
                  final order = orderController.orders[index];
                  return OrderCard(
                    orderId: order.id,
                    customerName: order.customerName,
                    amount: '₹${order.totalAmount.toStringAsFixed(2)}',
                    status: order.status.name,
                    date:
                        '${order.orderDate.day}/${order.orderDate.month}/${order.orderDate.year}',
                    statusColor: _getStatusColor(order.status),
                    onTap: () {
                      Get.to(() => OrderDetailsPage(order: order));
                    },
                  );
                },
              ),
          ],
        ),
      );
    });
  }

  Color _getStatusColor(status) {
    switch (status.toString()) {
      case 'OrderStatus.pending':
        return AppColors.pending;
      case 'OrderStatus.confirmed':
        return AppColors.confirmed;
      case 'OrderStatus.delivered':
        return AppColors.delivered;
      case 'OrderStatus.cancelled':
        return AppColors.cancelled;
      default:
        return AppColors.textSecondary;
    }
  }
}

class _OrdersManagement extends StatelessWidget {
  const _OrdersManagement();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Orders Management',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}

class _InventoryManagement extends StatelessWidget {
  const _InventoryManagement();

  @override
  Widget build(BuildContext context) {
    return ProductManagementPage();
  }
}

class _ReportsView extends StatelessWidget {
  const _ReportsView();

  @override
  Widget build(BuildContext context) {
    return const ReportsAnalyticsPage();
  }
}
