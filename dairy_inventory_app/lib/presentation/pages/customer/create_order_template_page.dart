import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/order_template.dart';
import '../../../models/milk_product.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_template_controller.dart';
import '../../controllers/product_controller.dart';

class CreateOrderTemplatePage extends StatefulWidget {
  const CreateOrderTemplatePage({super.key});

  @override
  State<CreateOrderTemplatePage> createState() =>
      _CreateOrderTemplatePageState();
}

class _CreateOrderTemplatePageState extends State<CreateOrderTemplatePage> {
  final AuthController _authController = Get.find<AuthController>();
  final OrderTemplateController _templateController =
      Get.find<OrderTemplateController>();
  final ProductController _productController = Get.put(ProductController());

  final _formKey = GlobalKey<FormState>();
  final _templateNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _specialInstructionsController = TextEditingController();

  final Map<String, double> _selectedProducts = {};
  DeliveryFrequency _selectedFrequency = DeliveryFrequency.daily;
  DeliveryTimePreference _selectedTimePreference =
      DeliveryTimePreference.morning;
  List<int> _customDeliveryDays = [];
  bool _isDefault = false;

  @override
  void initState() {
    super.initState();
    _productController.loadProducts();
  }

  @override
  void dispose() {
    _templateNameController.dispose();
    _descriptionController.dispose();
    _specialInstructionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Order Template'), elevation: 0),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildProductSelectionSection(),
              const SizedBox(height: 24),
              _buildDeliveryPreferencesSection(),
              const SizedBox(height: 24),
              _buildAdditionalOptionsSection(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _templateNameController,
            decoration: const InputDecoration(
              labelText: 'Template Name *',
              hintText: 'e.g., Daily Essentials, Weekend Special',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a template name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description (Optional)',
              hintText: 'Brief description of this template',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Checkbox(
                value: _isDefault,
                onChanged: (value) {
                  setState(() {
                    _isDefault = value ?? false;
                  });
                },
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Set as default template',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductSelectionSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Products',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Obx(() {
            if (_productController.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (_productController.products.isEmpty) {
              return const Center(child: Text('No products available'));
            }

            return Column(
              children:
                  _productController.products.map((product) {
                    final quantity = _selectedProducts[product.id] ?? 0.0;
                    return _buildProductItem(product, quantity);
                  }).toList(),
            );
          }),
          if (_selectedProducts.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Amount:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '₹${_calculateTotalAmount().toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductItem(MilkProduct product, double quantity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.border),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '₹${product.pricePerLiter}/liter',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: quantity > 0,
                onChanged: (value) {
                  setState(() {
                    if (value) {
                      _selectedProducts[product.id] = 1.0;
                    } else {
                      _selectedProducts.remove(product.id);
                    }
                  });
                },
              ),
            ],
          ),
          if (quantity > 0) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                IconButton(
                  onPressed: () {
                    setState(() {
                      if (quantity > 0.5) {
                        _selectedProducts[product.id] = quantity - 0.5;
                      } else {
                        _selectedProducts.remove(product.id);
                      }
                    });
                  },
                  icon: const Icon(Icons.remove),
                ),
                Expanded(
                  child: Text(
                    '${quantity.toStringAsFixed(1)} L',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _selectedProducts[product.id] = quantity + 0.5;
                    });
                  },
                  icon: const Icon(Icons.add),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDeliveryPreferencesSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Delivery Preferences',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<DeliveryFrequency>(
            value: _selectedFrequency,
            decoration: const InputDecoration(
              labelText: 'Delivery Frequency',
              border: OutlineInputBorder(),
            ),
            items:
                DeliveryFrequency.values.map((frequency) {
                  return DropdownMenuItem(
                    value: frequency,
                    child: Text(_getFrequencyDisplayName(frequency)),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedFrequency = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<DeliveryTimePreference>(
            value: _selectedTimePreference,
            decoration: const InputDecoration(
              labelText: 'Preferred Time',
              border: OutlineInputBorder(),
            ),
            items:
                DeliveryTimePreference.values.map((time) {
                  return DropdownMenuItem(
                    value: time,
                    child: Text(_getTimePreferenceDisplayName(time)),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedTimePreference = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalOptionsSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Additional Options',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _specialInstructionsController,
            decoration: const InputDecoration(
              labelText: 'Special Instructions (Optional)',
              hintText: 'Any special delivery instructions',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'Cancel',
            onPressed: () => Get.back(),
            type: ButtonType.outline,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: 'Create Template',
            onPressed: _createTemplate,
            isLoading: _templateController.isLoading,
          ),
        ),
      ],
    );
  }

  double _calculateTotalAmount() {
    double total = 0.0;
    _selectedProducts.forEach((productId, quantity) {
      final product = _productController.getProductById(productId);
      if (product != null) {
        total += product.pricePerLiter * quantity;
      }
    });
    return total;
  }

  String _getFrequencyDisplayName(DeliveryFrequency frequency) {
    switch (frequency) {
      case DeliveryFrequency.daily:
        return 'Daily';
      case DeliveryFrequency.everyOtherDay:
        return 'Every Other Day';
      case DeliveryFrequency.weekly:
        return 'Weekly';
      case DeliveryFrequency.biWeekly:
        return 'Bi-Weekly';
      case DeliveryFrequency.monthly:
        return 'Monthly';
      case DeliveryFrequency.custom:
        return 'Custom Schedule';
    }
  }

  String _getTimePreferenceDisplayName(DeliveryTimePreference preference) {
    switch (preference) {
      case DeliveryTimePreference.morning:
        return 'Morning (6:00 AM - 10:00 AM)';
      case DeliveryTimePreference.afternoon:
        return 'Afternoon (10:00 AM - 2:00 PM)';
      case DeliveryTimePreference.evening:
        return 'Evening (2:00 PM - 6:00 PM)';
      case DeliveryTimePreference.flexible:
        return 'Flexible';
    }
  }

  void _createTemplate() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedProducts.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select at least one product',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final customerId = _authController.currentUser?.id;
    if (customerId == null) {
      Get.snackbar(
        'Error',
        'User not found',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final templateItems =
        _selectedProducts.entries.map((entry) {
          final product = _productController.getProductById(entry.key)!;
          return OrderTemplateItem(
            productId: product.id,
            productName: product.name,
            productCategory: product.category.name,
            quantity: entry.value,
            unitPrice: product.pricePerLiter,
          );
        }).toList();

    final template = OrderTemplate(
      id: 'template_${DateTime.now().millisecondsSinceEpoch}',
      customerId: customerId,
      templateName: _templateNameController.text.trim(),
      description:
          _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
      items: templateItems,
      deliveryFrequency: _selectedFrequency,
      timePreference: _selectedTimePreference,
      customDeliveryDays: _customDeliveryDays,
      specialInstructions:
          _specialInstructionsController.text.trim().isEmpty
              ? null
              : _specialInstructionsController.text.trim(),
      isDefault: _isDefault,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final success = await _templateController.createTemplate(template);
    if (success) {
      Get.back();
    }
  }
}
