import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/empty_state.dart';
import '../../../models/order_template.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_template_controller.dart';
import '../../controllers/order_controller.dart';
import 'create_order_template_page.dart';
import 'edit_order_template_page.dart';
import '../orders/create_order_page.dart';

class OrderTemplatesPage extends StatefulWidget {
  const OrderTemplatesPage({super.key});

  @override
  State<OrderTemplatesPage> createState() => _OrderTemplatesPageState();
}

class _OrderTemplatesPageState extends State<OrderTemplatesPage> {
  final OrderTemplateController _templateController = Get.put(
    OrderTemplateController(),
  );
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final customerId = _authController.currentUser?.id;
      if (customerId != null) {
        _templateController.loadTemplates(customerId: customerId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Order Templates'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _navigateToCreateTemplate,
          ),
        ],
      ),
      body: Obx(() {
        if (_templateController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_templateController.error.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: AppColors.error),
                const SizedBox(height: 16),
                Text(
                  'Error loading templates',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  _templateController.error,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 24),
                CustomButton(
                  text: 'Retry',
                  onPressed: () {
                    final customerId = _authController.currentUser?.id;
                    if (customerId != null) {
                      _templateController.loadTemplates(customerId: customerId);
                    }
                  },
                ),
              ],
            ),
          );
        }

        if (!_templateController.hasTemplates) {
          return _buildEmptyState();
        }

        return _buildTemplatesList();
      }),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToCreateTemplate,
        icon: const Icon(Icons.add),
        label: const Text('New Template'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  Widget _buildEmptyState() {
    return EmptyState(
      icon: Icons.list_alt,
      title: 'No Order Templates',
      subtitle:
          'Create your first order template to save time on future orders',
      actionText: 'Create Template',
      onActionPressed: _navigateToCreateTemplate,
    );
  }

  Widget _buildTemplatesList() {
    return RefreshIndicator(
      onRefresh: () async {
        final customerId = _authController.currentUser?.id;
        if (customerId != null) {
          await _templateController.loadTemplates(customerId: customerId);
        }
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _templateController.templates.length,
        itemBuilder: (context, index) {
          final template = _templateController.templates[index];
          return _buildTemplateCard(template);
        },
      ),
    );
  }

  Widget _buildTemplateCard(OrderTemplate template) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and actions
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            template.templateName,
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ),
                        if (template.isDefault)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'DEFAULT',
                              style: TextStyle(
                                color: AppColors.primary,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (template.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        template.description!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(value, template),
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'duplicate',
                        child: Row(
                          children: [
                            Icon(Icons.copy, size: 20),
                            SizedBox(width: 8),
                            Text('Duplicate'),
                          ],
                        ),
                      ),
                      if (!template.isDefault)
                        const PopupMenuItem(
                          value: 'setDefault',
                          child: Row(
                            children: [
                              Icon(Icons.star, size: 20),
                              SizedBox(width: 8),
                              Text('Set as Default'),
                            ],
                          ),
                        ),
                      PopupMenuItem(
                        value: 'toggle',
                        child: Row(
                          children: [
                            Icon(
                              template.isActive
                                  ? Icons.pause
                                  : Icons.play_arrow,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(template.isActive ? 'Deactivate' : 'Activate'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Template details
          _buildTemplateDetails(template),

          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed:
                      template.isActive
                          ? () => _createOrderFromTemplate(template)
                          : null,
                  icon: const Icon(Icons.shopping_cart, size: 18),
                  label: const Text('Order Now'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomButton(
                  text: 'Edit',
                  onPressed: () => _navigateToEditTemplate(template),
                  type: ButtonType.outline,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateDetails(OrderTemplate template) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Items summary
        Row(
          children: [
            Icon(Icons.inventory_2, size: 16, color: AppColors.textSecondary),
            const SizedBox(width: 8),
            Text(
              '${template.activeItemCount} items',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(width: 16),
            Icon(
              Icons.currency_rupee,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 4),
            Text(
              '₹${template.totalAmount.toStringAsFixed(2)}',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Delivery details
        Row(
          children: [
            Icon(Icons.schedule, size: 16, color: AppColors.textSecondary),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                template.deliveryFrequencyDisplayName,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
            Icon(Icons.access_time, size: 16, color: AppColors.textSecondary),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                template.timePreferenceDisplayName.split(' ').first,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
          ],
        ),

        // Status indicator
        if (!template.isActive) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.pause, size: 14, color: AppColors.warning),
                const SizedBox(width: 4),
                Text(
                  'Inactive',
                  style: TextStyle(
                    color: AppColors.warning,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  void _navigateToCreateTemplate() {
    Get.to(() => const CreateOrderTemplatePage());
  }

  void _navigateToEditTemplate(OrderTemplate template) {
    Get.to(() => EditOrderTemplatePage(template: template));
  }

  void _handleMenuAction(String action, OrderTemplate template) {
    switch (action) {
      case 'edit':
        _navigateToEditTemplate(template);
        break;
      case 'duplicate':
        _duplicateTemplate(template);
        break;
      case 'setDefault':
        _templateController.setAsDefault(template.id);
        break;
      case 'toggle':
        _templateController.toggleTemplateStatus(template.id);
        break;
      case 'delete':
        _confirmDeleteTemplate(template);
        break;
    }
  }

  void _duplicateTemplate(OrderTemplate template) {
    final duplicatedTemplate = template.copyWith(
      id: 'template_${DateTime.now().millisecondsSinceEpoch}',
      templateName: '${template.templateName} (Copy)',
      isDefault: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Get.to(
      () => EditOrderTemplatePage(template: duplicatedTemplate, isNew: true),
    );
  }

  void _confirmDeleteTemplate(OrderTemplate template) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Template'),
        content: Text(
          'Are you sure you want to delete "${template.templateName}"?',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          TextButton(
            onPressed: () {
              Get.back();
              _templateController.deleteTemplate(template.id);
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _createOrderFromTemplate(OrderTemplate template) {
    // Convert template to order items and navigate to order creation
    final orderItems = template.toOrderItems();

    Get.to(
      () => CreateOrderPage(
        prefilledItems: orderItems,
        templateName: template.templateName,
      ),
    );
  }
}
