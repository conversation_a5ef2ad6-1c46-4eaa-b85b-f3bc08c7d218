import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_controller.dart';
import '../orders/create_order_page.dart';
import '../orders/order_details_page.dart';

class CustomerDashboard extends StatefulWidget {
  const CustomerDashboard({super.key});

  @override
  State<CustomerDashboard> createState() => _CustomerDashboardState();
}

class _CustomerDashboardState extends State<CustomerDashboard>
    with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _selectedIndex = _tabController.index;
        });
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final customerId = _authController.currentUser?.id;
      if (customerId != null) {
        _orderController.loadOrders(customerId: customerId);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _logout() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await _authController.logout();
        Get.snackbar(
          'Success',
          'Logged out successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: AppColors.textOnPrimary,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to logout. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error,
          colorText: AppColors.textOnPrimary,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.secondary,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.secondaryGradient,
          ),
        ),
        title: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.surface,
              child: Icon(Icons.person, color: AppColors.secondary, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Hello!',
                    style: TextStyle(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    _authController.currentUser?.name ?? 'Customer',
                    style: const TextStyle(
                      color: AppColors.textOnPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _logout,
            icon: const Icon(Icons.logout, color: AppColors.textOnPrimary),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          _CustomerOverview(),
          _CreateOrder(),
          _OrderHistory(),
          _PaymentHistory(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });
            _tabController.animateTo(index);
          },
          labelColor: AppColors.secondary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.secondary,
          indicatorWeight: 3,
          labelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.home), text: 'Home'),
            Tab(icon: Icon(Icons.add_shopping_cart), text: 'New Order'),
            Tab(icon: Icon(Icons.history), text: 'Orders'),
            Tab(icon: Icon(Icons.payment), text: 'Payments'),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Quick order action
        },
        backgroundColor: AppColors.secondary,
        child: const Icon(Icons.add),
      ),
    );
  }
}

class _CustomerOverview extends StatelessWidget {
  const _CustomerOverview();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      if (orderController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final totalOrders = orderController.orders.length;
      final pendingAmount = orderController.getPendingAmount();
      final todaysOrders = orderController.getTodaysOrders();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Quick Actions
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),

            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 120,
                    decoration: BoxDecoration(
                      gradient: AppColors.secondaryGradient,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Get.to(() => const CreateOrderPage());
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: const Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_shopping_cart,
                                color: AppColors.textOnPrimary,
                                size: 32,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'New Order',
                                style: TextStyle(
                                  color: AppColors.textOnPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    height: 120,
                    decoration: BoxDecoration(
                      gradient: AppColors.accentGradient,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          // Navigate to order history
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: const Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.history,
                                color: AppColors.textOnPrimary,
                                size: 32,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Order History',
                                style: TextStyle(
                                  color: AppColors.textOnPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing24),

            // Account Summary
            const Text(
              'Account Summary',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              mainAxisSpacing: AppConstants.spacing16,
              crossAxisSpacing: AppConstants.spacing16,
              children: [
                StatCard(
                  title: 'Total Orders',
                  value: '$totalOrders',
                  icon: Icons.shopping_cart,
                  iconColor: AppColors.secondary,
                ),
                StatCard(
                  title: 'Today\'s Orders',
                  value: '${todaysOrders.length}',
                  icon: Icons.today,
                  iconColor: AppColors.primary,
                ),
                StatCard(
                  title: 'Pending Amount',
                  value: '₹${pendingAmount.toStringAsFixed(0)}',
                  icon: Icons.pending_actions,
                  iconColor: AppColors.warning,
                ),
                StatCard(
                  title: 'This Month',
                  value: '₹0',
                  icon: Icons.calendar_month,
                  iconColor: AppColors.info,
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing24),

            // Recent Orders
            Row(
              children: [
                const Text(
                  'Recent Orders',
                  style: TextStyle(
                    fontSize: AppConstants.fontXLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to orders tab
                  },
                  child: const Text('View All'),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing16),

            if (orderController.orders.isEmpty)
              const EmptyState(
                icon: Icons.shopping_cart_outlined,
                title: 'No Orders Yet',
                subtitle: 'Start by creating your first order!',
                buttonText: 'Create Order',
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: orderController.orders.take(3).length,
                itemBuilder: (context, index) {
                  final order = orderController.orders[index];
                  return OrderCard(
                    orderId: order.id,
                    customerName: 'Order #${order.id.substring(0, 8)}',
                    amount: '₹${order.totalAmount.toStringAsFixed(2)}',
                    status: order.status.name,
                    date:
                        '${order.orderDate.day}/${order.orderDate.month}/${order.orderDate.year}',
                    statusColor: _getStatusColor(order.status),
                    onTap: () {
                      Get.to(() => OrderDetailsPage(order: order));
                    },
                  );
                },
              ),
          ],
        ),
      );
    });
  }

  Color _getStatusColor(status) {
    switch (status.toString()) {
      case 'OrderStatus.pending':
        return AppColors.pending;
      case 'OrderStatus.confirmed':
        return AppColors.confirmed;
      case 'OrderStatus.delivered':
        return AppColors.delivered;
      case 'OrderStatus.cancelled':
        return AppColors.cancelled;
      default:
        return AppColors.textSecondary;
    }
  }
}

class _CreateOrder extends StatelessWidget {
  const _CreateOrder();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Create New Order',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}

class _OrderHistory extends StatelessWidget {
  const _OrderHistory();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Order History',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}

class _PaymentHistory extends StatelessWidget {
  const _PaymentHistory();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Payment History',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}
