import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_controller.dart';
import '../../../models/order.dart';
import '../delivery/delivery_management_page.dart';

class FieldManDashboard extends StatefulWidget {
  const FieldManDashboard({super.key});

  @override
  State<FieldManDashboard> createState() => _FieldManDashboardState();
}

class _FieldManDashboardState extends State<FieldManDashboard>
    with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
      animationDuration: Duration.zero, // Remove animation to prevent shaking
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final fieldManId = _authController.currentUser?.id;
      if (fieldManId != null) {
        _orderController.loadOrders(fieldManId: fieldManId);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _logout() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await _authController.logout();
        Get.snackbar(
          'Success',
          'Logged out successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: AppColors.textOnPrimary,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to logout. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error,
          colorText: AppColors.textOnPrimary,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.accent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppColors.accentGradient),
        ),
        title: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.surface,
              child: Icon(
                Icons.local_shipping,
                color: AppColors.accent,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Ready to deliver!',
                    style: TextStyle(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    _authController.currentUser?.name ?? 'Field Staff',
                    style: const TextStyle(
                      color: AppColors.textOnPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _logout,
            icon: const Icon(Icons.logout, color: AppColors.textOnPrimary),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          _FieldManOverview(),
          _DeliveryList(),
          _CashCollection(),
          _DeliveryHistory(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          onTap: (index) {
            // Simple, direct tab switching without animations
            _tabController.index = index;
            setState(() {
              _selectedIndex = index;
            });
          },
          labelColor: AppColors.accent,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.accent,
          indicatorWeight: 3,
          labelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.home), text: 'Home'),
            Tab(icon: Icon(Icons.local_shipping), text: 'Deliveries'),
            Tab(icon: Icon(Icons.payments), text: 'Collection'),
            Tab(icon: Icon(Icons.history), text: 'History'),
          ],
        ),
      ),
    );
  }
}

class _FieldManOverview extends StatelessWidget {
  const _FieldManOverview();

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    return Obx(() {
      if (orderController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final todaysOrders = orderController.getTodaysOrders();
      final pendingDeliveries = orderController.getOrdersByStatus(
        OrderStatus.confirmed,
      );
      final deliveredOrders = orderController.getDeliveredOrdersCount();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Today's Summary
            const Text(
              'Today\'s Summary',
              style: TextStyle(
                fontSize: AppConstants.fontXLarge,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              mainAxisSpacing: AppConstants.spacing16,
              crossAxisSpacing: AppConstants.spacing16,
              children: [
                StatCard(
                  title: 'Today\'s Orders',
                  value: '${todaysOrders.length}',
                  icon: Icons.local_shipping,
                  iconColor: AppColors.accent,
                ),
                StatCard(
                  title: 'Pending Deliveries',
                  value: '${pendingDeliveries.length}',
                  icon: Icons.pending_actions,
                  iconColor: AppColors.warning,
                ),
                StatCard(
                  title: 'Delivered',
                  value: '$deliveredOrders',
                  icon: Icons.check_circle,
                  iconColor: AppColors.success,
                ),
                StatCard(
                  title: 'Cash Collected',
                  value: '₹0',
                  icon: Icons.payments,
                  iconColor: AppColors.primary,
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing24),

            // Pending Deliveries
            Row(
              children: [
                const Text(
                  'Pending Deliveries',
                  style: TextStyle(
                    fontSize: AppConstants.fontXLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                if (pendingDeliveries.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.spacing12,
                      vertical: AppConstants.spacing4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.warning.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusLarge,
                      ),
                    ),
                    child: Text(
                      '${pendingDeliveries.length} pending',
                      style: const TextStyle(
                        color: AppColors.warning,
                        fontWeight: FontWeight.w600,
                        fontSize: AppConstants.fontSmall,
                      ),
                    ),
                  ),
              ],
            ),

            const SizedBox(height: AppConstants.spacing16),

            if (pendingDeliveries.isEmpty)
              const EmptyState(
                icon: Icons.check_circle_outline,
                title: 'All Deliveries Completed!',
                subtitle: 'Great job! No pending deliveries for today.',
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: pendingDeliveries.length,
                itemBuilder: (context, index) {
                  final order = pendingDeliveries[index];
                  return CustomCard(
                    margin: const EdgeInsets.only(
                      bottom: AppConstants.spacing12,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    order.customerName,
                                    style: const TextStyle(
                                      fontSize: AppConstants.fontLarge,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                  const SizedBox(height: AppConstants.spacing4),
                                  Text(
                                    'Order #${order.id.substring(0, 8)}',
                                    style: const TextStyle(
                                      fontSize: AppConstants.fontMedium,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              '₹${order.totalAmount.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: AppConstants.fontLarge,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppConstants.spacing16),
                        Row(
                          children: [
                            Expanded(
                              child: CustomButton(
                                text: 'Mark Delivered',
                                onPressed: () {
                                  orderController.updateOrderStatus(
                                    order.id,
                                    OrderStatus.delivered,
                                  );
                                },
                                type: ButtonType.primary,
                              ),
                            ),
                            const SizedBox(width: AppConstants.spacing12),
                            CustomButton(
                              text: 'Call',
                              onPressed: () {
                                // Call customer
                              },
                              type: ButtonType.outline,
                              icon: Icons.phone,
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      );
    });
  }
}

class _DeliveryList extends StatelessWidget {
  const _DeliveryList();

  @override
  Widget build(BuildContext context) {
    return const DeliveryManagementPage();
  }
}

class _CashCollection extends StatelessWidget {
  const _CashCollection();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Cash Collection',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}

class _DeliveryHistory extends StatelessWidget {
  const _DeliveryHistory();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: EmptyState(
        icon: Icons.construction,
        title: 'Delivery History',
        subtitle: 'This feature is coming soon!',
      ),
    );
  }
}
