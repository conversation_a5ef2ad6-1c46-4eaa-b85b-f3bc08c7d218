import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/milk_product.dart';
import '../../../models/order.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_controller.dart';
import '../../controllers/product_controller.dart';

class CreateOrderPage extends StatefulWidget {
  const CreateOrderPage({super.key});

  @override
  State<CreateOrderPage> createState() => _CreateOrderPageState();
}

class _CreateOrderPageState extends State<CreateOrderPage> {
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();
  final ProductController _productController = Get.put(ProductController());

  final Map<String, double> _selectedProducts = {};
  final TextEditingController _notesController = TextEditingController();

  double get totalAmount {
    double total = 0.0;
    _selectedProducts.forEach((productId, quantity) {
      final product = _productController.getProductById(productId);
      if (product != null) {
        total += product.pricePerLiter * quantity;
      }
    });
    return total;
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create New Order'), elevation: 0),
      body: Obx(() {
        if (_productController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            // Product Selection
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.spacing16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Category Filter
                    _buildCategoryFilter(),

                    const SizedBox(height: AppConstants.spacing16),

                    // Products List
                    _buildProductsList(),

                    const SizedBox(height: AppConstants.spacing24),

                    // Order Notes
                    _buildOrderNotes(),
                  ],
                ),
              ),
            ),

            // Order Summary and Create Button
            _buildOrderSummary(),
          ],
        );
      }),
    );
  }

  Widget _buildCategoryFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Category',
          style: TextStyle(
            fontSize: AppConstants.fontLarge,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppConstants.spacing12),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _productController.categories.length,
            itemBuilder: (context, index) {
              final category = _productController.categories[index];
              final isSelected =
                  _productController.selectedCategory == category;

              return Padding(
                padding: const EdgeInsets.only(right: AppConstants.spacing8),
                child: FilterChip(
                  label: Text(category.toUpperCase()),
                  selected: isSelected,
                  onSelected: (_) => _productController.setCategory(category),
                  backgroundColor: AppColors.surfaceVariant,
                  selectedColor: AppColors.primary,
                  labelStyle: TextStyle(
                    color:
                        isSelected
                            ? AppColors.textOnPrimary
                            : AppColors.textPrimary,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductsList() {
    final products = _productController.getFilteredProducts();

    if (products.isEmpty) {
      return const EmptyState(
        icon: Icons.inventory_2_outlined,
        title: 'No Products Available',
        subtitle: 'No products found in this category.',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Products',
          style: TextStyle(
            fontSize: AppConstants.fontLarge,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppConstants.spacing12),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            final quantity = _selectedProducts[product.id] ?? 0.0;

            return _buildProductCard(product, quantity);
          },
        ),
      ],
    );
  }

  Widget _buildProductCard(MilkProduct product, double quantity) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.spacing12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(
                    AppConstants.radiusMedium,
                  ),
                ),
                child: const Icon(
                  Icons.local_drink,
                  color: AppColors.textOnPrimary,
                  size: AppConstants.iconLarge,
                ),
              ),
              const SizedBox(width: AppConstants.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: AppConstants.fontLarge,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacing4),
                    Text(
                      '${product.fatContent}% Fat • ${product.category.name}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontMedium,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacing4),
                    Text(
                      '₹${product.pricePerLiter.toStringAsFixed(2)}/L',
                      style: const TextStyle(
                        fontSize: AppConstants.fontLarge,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          if (quantity > 0) ...[
            const SizedBox(height: AppConstants.spacing16),
            Container(
              padding: const EdgeInsets.all(AppConstants.spacing12),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed:
                        () => _updateQuantity(product.id, quantity - 0.5),
                    icon: const Icon(Icons.remove),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.textOnPrimary,
                      minimumSize: const Size(40, 40),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      '${quantity.toStringAsFixed(1)} L',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: AppConstants.fontLarge,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed:
                        () => _updateQuantity(product.id, quantity + 0.5),
                    icon: const Icon(Icons.add),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.textOnPrimary,
                      minimumSize: const Size(40, 40),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: AppConstants.spacing12),

          CustomButton(
            text: quantity > 0 ? 'Update Quantity' : 'Add to Order',
            onPressed:
                () =>
                    quantity > 0
                        ? _showQuantityDialog(product)
                        : _updateQuantity(product.id, 1.0),
            type: quantity > 0 ? ButtonType.outline : ButtonType.primary,
            isExpanded: true,
            icon: quantity > 0 ? Icons.edit : Icons.add_shopping_cart,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderNotes() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Order Notes (Optional)',
          style: TextStyle(
            fontSize: AppConstants.fontLarge,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppConstants.spacing12),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'Add any special instructions or notes...',
            border: OutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacing16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            Row(
              children: [
                const Text(
                  'Total Items: ',
                  style: TextStyle(
                    fontSize: AppConstants.fontLarge,
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  '${_selectedProducts.length}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                const Text(
                  'Total: ',
                  style: TextStyle(
                    fontSize: AppConstants.fontLarge,
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  '₹${totalAmount.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontXLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.spacing16),
            CustomButton(
              text: 'Create Order',
              onPressed: _selectedProducts.isNotEmpty ? _createOrder : null,
              isExpanded: true,
              icon: Icons.shopping_cart_checkout,
            ),
          ],
        ),
      ),
    );
  }

  void _updateQuantity(String productId, double quantity) {
    setState(() {
      if (quantity <= 0) {
        _selectedProducts.remove(productId);
      } else {
        _selectedProducts[productId] = quantity;
      }
    });
  }

  void _showQuantityDialog(MilkProduct product) {
    final currentQuantity = _selectedProducts[product.id] ?? 0.0;
    final controller = TextEditingController(text: currentQuantity.toString());

    Get.dialog(
      AlertDialog(
        title: Text('Update ${product.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Quantity (Liters)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              final quantity = double.tryParse(controller.text) ?? 0.0;
              _updateQuantity(product.id, quantity);
              Get.back();
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  Future<void> _createOrder() async {
    if (_selectedProducts.isEmpty) return;

    final user = _authController.currentUser;
    if (user == null) return;

    // Create order items
    final List<OrderItem> items = [];
    _selectedProducts.forEach((productId, quantity) {
      final product = _productController.getProductById(productId);
      if (product != null) {
        items.add(
          OrderItem(
            productId: productId,
            productName: product.name,
            productCategory: product.category.name,
            quantity: quantity,
            unitPrice: product.pricePerLiter,
            totalPrice: product.pricePerLiter * quantity,
          ),
        );
      }
    });

    final order = Order(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      customerId: user.id,
      customerName: user.name,
      items: items,
      totalAmount: totalAmount,
      pendingAmount: totalAmount,
      status: OrderStatus.pending,
      paymentStatus: PaymentStatus.pending,
      orderDate: DateTime.now(),
      notes:
          _notesController.text.trim().isNotEmpty
              ? _notesController.text.trim()
              : null,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final success = await _orderController.createOrder(order);
    if (success) {
      Get.back();
      Get.snackbar(
        'Success',
        'Order created successfully!',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
