import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/order.dart';
import '../../../models/payment.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/order_controller.dart';
import '../../controllers/payment_controller.dart';

class OrderDetailsPage extends StatefulWidget {
  final Order order;

  const OrderDetailsPage({super.key, required this.order});

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  final AuthController _authController = Get.find<AuthController>();
  final OrderController _orderController = Get.find<OrderController>();
  final PaymentController _paymentController = Get.put(PaymentController());

  late Order _currentOrder;

  @override
  void initState() {
    super.initState();
    _currentOrder = widget.order;
    _loadPayments();
  }

  void _loadPayments() {
    _paymentController.loadPayments(orderId: _currentOrder.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Order #${_currentOrder.id.substring(0, 8)}'),
        actions: [
          if (_authController.isAdmin())
            PopupMenuButton<String>(
              onSelected: _handleMenuAction,
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit Order'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'cancel',
                      child: Row(
                        children: [
                          Icon(Icons.cancel, color: AppColors.error),
                          SizedBox(width: 8),
                          Text('Cancel Order'),
                        ],
                      ),
                    ),
                  ],
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Status Card
            _buildOrderStatusCard(),

            const SizedBox(height: AppConstants.spacing16),

            // Customer Information
            _buildCustomerInfoCard(),

            const SizedBox(height: AppConstants.spacing16),

            // Order Items
            _buildOrderItemsCard(),

            const SizedBox(height: AppConstants.spacing16),

            // Payment Information
            _buildPaymentInfoCard(),

            const SizedBox(height: AppConstants.spacing16),

            // Payment History
            _buildPaymentHistoryCard(),

            const SizedBox(height: AppConstants.spacing16),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderStatusCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.spacing12,
                  vertical: AppConstants.spacing8,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    _currentOrder.status,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                ),
                child: Text(
                  _currentOrder.status.name.toUpperCase(),
                  style: TextStyle(
                    color: _getStatusColor(_currentOrder.status),
                    fontWeight: FontWeight.bold,
                    fontSize: AppConstants.fontSmall,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                '₹${_currentOrder.totalAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: AppConstants.fontXLarge,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.spacing16),
          _buildInfoRow('Order Date', _formatDate(_currentOrder.orderDate)),
          if (_currentOrder.deliveryDate != null)
            _buildInfoRow(
              'Delivery Date',
              _formatDate(_currentOrder.deliveryDate!),
            ),
          if (_currentOrder.fieldManName != null)
            _buildInfoRow('Assigned to', _currentOrder.fieldManName!),
          if (_currentOrder.notes != null)
            _buildInfoRow('Notes', _currentOrder.notes!),
        ],
      ),
    );
  }

  Widget _buildCustomerInfoCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Customer Information',
            style: TextStyle(
              fontSize: AppConstants.fontLarge,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.spacing12),
          Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                child: const Icon(Icons.person, color: AppColors.primary),
              ),
              const SizedBox(width: AppConstants.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentOrder.customerName,
                      style: const TextStyle(
                        fontSize: AppConstants.fontLarge,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      'Customer ID: ${_currentOrder.customerId.substring(0, 8)}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontMedium,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {
                  // Call customer
                },
                icon: const Icon(Icons.phone),
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.success.withValues(alpha: 0.1),
                  foregroundColor: AppColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItemsCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Order Items',
            style: TextStyle(
              fontSize: AppConstants.fontLarge,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.spacing16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _currentOrder.items.length,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final item = _currentOrder.items[index];
              return Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: AppColors.primaryGradient,
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                    child: const Icon(
                      Icons.local_drink,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                  const SizedBox(width: AppConstants.spacing16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.productName,
                          style: const TextStyle(
                            fontSize: AppConstants.fontLarge,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        Text(
                          '${item.quantity.toStringAsFixed(1)} L × ₹${item.unitPrice.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontMedium,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    '₹${item.totalPrice.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontLarge,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Payment Information',
            style: TextStyle(
              fontSize: AppConstants.fontLarge,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.spacing16),
          _buildPaymentRow(
            'Total Amount',
            _currentOrder.totalAmount,
            AppColors.textPrimary,
          ),
          _buildPaymentRow(
            'Paid Amount',
            _currentOrder.paidAmount,
            AppColors.success,
          ),
          _buildPaymentRow(
            'Pending Amount',
            _currentOrder.pendingAmount,
            AppColors.warning,
          ),
          const SizedBox(height: AppConstants.spacing12),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.spacing12,
              vertical: AppConstants.spacing8,
            ),
            decoration: BoxDecoration(
              color: _getPaymentStatusColor(
                _currentOrder.paymentStatus,
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
            ),
            child: Text(
              _currentOrder.paymentStatus.name.toUpperCase(),
              style: TextStyle(
                color: _getPaymentStatusColor(_currentOrder.paymentStatus),
                fontWeight: FontWeight.bold,
                fontSize: AppConstants.fontSmall,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentHistoryCard() {
    return Obx(() {
      final payments = _paymentController.getPaymentsByOrder(_currentOrder.id);

      return CustomCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Payment History',
                  style: TextStyle(
                    fontSize: AppConstants.fontLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                if (_currentOrder.pendingAmount > 0)
                  TextButton.icon(
                    onPressed: _showAddPaymentDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Payment'),
                  ),
              ],
            ),
            const SizedBox(height: AppConstants.spacing16),
            if (payments.isEmpty)
              const Text(
                'No payments recorded yet',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: payments.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final payment = payments[index];
                  return Row(
                    children: [
                      Icon(
                        _getPaymentMethodIcon(payment.method),
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: AppConstants.spacing12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              payment.method.name.toUpperCase(),
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            Text(
                              _formatDate(payment.paymentDate),
                              style: const TextStyle(
                                fontSize: AppConstants.fontSmall,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '₹${payment.amount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontLarge,
                          fontWeight: FontWeight.bold,
                          color: AppColors.success,
                        ),
                      ),
                    ],
                  );
                },
              ),
          ],
        ),
      );
    });
  }

  Widget _buildActionButtons() {
    final user = _authController.currentUser;
    if (user == null) return const SizedBox.shrink();

    return Column(
      children: [
        if (_authController.isFieldMan() &&
            _currentOrder.status == OrderStatus.confirmed)
          CustomButton(
            text: 'Mark as Delivered',
            onPressed: _markAsDelivered,
            isExpanded: true,
            icon: Icons.check_circle,
          ),

        if (_authController.isAdmin()) ...[
          if (_currentOrder.status == OrderStatus.pending)
            CustomButton(
              text: 'Confirm Order',
              onPressed: _confirmOrder,
              isExpanded: true,
              icon: Icons.check,
            ),

          const SizedBox(height: AppConstants.spacing12),

          if (_currentOrder.fieldManId == null)
            CustomButton(
              text: 'Assign Field Staff',
              onPressed: _showAssignFieldStaffDialog,
              type: ButtonType.outline,
              isExpanded: true,
              icon: Icons.person_add,
            ),
        ],
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacing8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: AppConstants.fontMedium,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: AppColors.textPrimary,
                fontSize: AppConstants.fontMedium,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(String label, double amount, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacing8),
      child: Row(
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontSize: AppConstants.fontMedium,
            ),
          ),
          const Spacer(),
          Text(
            '₹${amount.toStringAsFixed(2)}',
            style: TextStyle(
              color: color,
              fontSize: AppConstants.fontMedium,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return AppColors.pending;
      case OrderStatus.confirmed:
        return AppColors.confirmed;
      case OrderStatus.inProgress:
        return AppColors.inProgress;
      case OrderStatus.delivered:
        return AppColors.delivered;
      case OrderStatus.cancelled:
        return AppColors.cancelled;
      case OrderStatus.onHold:
        return AppColors.textSecondary;
      case OrderStatus.returned:
        return AppColors.warning;
    }
  }

  Color _getPaymentStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return AppColors.warning;
      case PaymentStatus.paid:
        return AppColors.success;
      case PaymentStatus.partiallyPaid:
        return AppColors.info;
      case PaymentStatus.overdue:
        return AppColors.error;
      case PaymentStatus.refunded:
        return AppColors.textSecondary;
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.online:
        return Icons.credit_card;
      case PaymentMethod.upi:
        return Icons.qr_code;
      case PaymentMethod.cheque:
        return Icons.receipt;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        // Navigate to edit order page
        break;
      case 'cancel':
        _cancelOrder();
        break;
    }
  }

  Future<void> _confirmOrder() async {
    final success = await _orderController.updateOrderStatus(
      _currentOrder.id,
      OrderStatus.confirmed,
    );
    if (success) {
      setState(() {
        _currentOrder = _currentOrder.copyWith(status: OrderStatus.confirmed);
      });
    }
  }

  Future<void> _markAsDelivered() async {
    final success = await _orderController.updateOrderStatus(
      _currentOrder.id,
      OrderStatus.delivered,
    );
    if (success) {
      setState(() {
        _currentOrder = _currentOrder.copyWith(
          status: OrderStatus.delivered,
          deliveryDate: DateTime.now(),
        );
      });
    }
  }

  Future<void> _cancelOrder() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text('Are you sure you want to cancel this order?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _orderController.updateOrderStatus(
        _currentOrder.id,
        OrderStatus.cancelled,
      );
      if (success) {
        setState(() {
          _currentOrder = _currentOrder.copyWith(status: OrderStatus.cancelled);
        });
      }
    }
  }

  void _showAssignFieldStaffDialog() {
    // TODO: Implement field staff assignment dialog
    Get.snackbar(
      'Coming Soon',
      'Field staff assignment feature will be implemented',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _showAddPaymentDialog() {
    final amountController = TextEditingController();
    PaymentMethod selectedMethod = PaymentMethod.cash;
    final notesController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Add Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText:
                    'Amount (Max: ₹${_currentOrder.pendingAmount.toStringAsFixed(2)})',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppConstants.spacing16),
            DropdownButtonFormField<PaymentMethod>(
              value: selectedMethod,
              decoration: const InputDecoration(
                labelText: 'Payment Method',
                border: OutlineInputBorder(),
              ),
              items:
                  PaymentMethod.values.map((method) {
                    return DropdownMenuItem(
                      value: method,
                      child: Text(method.name.toUpperCase()),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) selectedMethod = value;
              },
            ),
            const SizedBox(height: AppConstants.spacing16),
            TextFormField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () async {
              final amount = double.tryParse(amountController.text) ?? 0.0;
              if (amount > 0 && amount <= _currentOrder.pendingAmount) {
                final success = await _paymentController.processPayment(
                  orderId: _currentOrder.id,
                  customerId: _currentOrder.customerId,
                  customerName: _currentOrder.customerName,
                  fieldManId: _currentOrder.fieldManId,
                  fieldManName: _currentOrder.fieldManName,
                  amount: amount,
                  method: selectedMethod,
                  notes:
                      notesController.text.trim().isNotEmpty
                          ? notesController.text.trim()
                          : null,
                );

                if (success) {
                  setState(() {
                    _currentOrder = _currentOrder.copyWith(
                      paidAmount: _currentOrder.paidAmount + amount,
                      pendingAmount: _currentOrder.pendingAmount - amount,
                      paymentStatus:
                          _currentOrder.pendingAmount - amount <= 0
                              ? PaymentStatus.paid
                              : PaymentStatus.partiallyPaid,
                    );
                  });
                  _loadPayments();
                  Get.back();
                }
              } else {
                Get.snackbar(
                  'Invalid Amount',
                  'Please enter a valid amount',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            child: const Text('Add Payment'),
          ),
        ],
      ),
    );
  }
}
