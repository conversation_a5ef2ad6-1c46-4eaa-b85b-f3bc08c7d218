import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/milk_product.dart';
import '../../controllers/product_controller.dart';

class ProductManagementPage extends StatefulWidget {
  const ProductManagementPage({super.key});

  @override
  State<ProductManagementPage> createState() => _ProductManagementPageState();
}

class _ProductManagementPageState extends State<ProductManagementPage> {
  final ProductController _productController = Get.put(ProductController());
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Product Management'),
        actions: [
          IconButton(
            onPressed: _showAddProductDialog,
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          _buildSearchAndFilter(),
          
          // Products List
          Expanded(
            child: Obx(() {
              if (_productController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              final products = _productController.getFilteredProducts();
              
              if (products.isEmpty) {
                return const EmptyState(
                  icon: Icons.inventory_2_outlined,
                  title: 'No Products Found',
                  subtitle: 'Add your first product to get started.',
                  buttonText: 'Add Product',
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(AppConstants.spacing16),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return _buildProductCard(product);
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddProductDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacing16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search Bar
          TextFormField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Search products...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              // TODO: Implement search functionality
            },
          ),
          
          const SizedBox(height: AppConstants.spacing12),
          
          // Category Filter
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _productController.categories.length,
              itemBuilder: (context, index) {
                final category = _productController.categories[index];
                final isSelected = _productController.selectedCategory == category;
                
                return Padding(
                  padding: const EdgeInsets.only(right: AppConstants.spacing8),
                  child: FilterChip(
                    label: Text(category.toUpperCase()),
                    selected: isSelected,
                    onSelected: (_) => _productController.setCategory(category),
                    backgroundColor: AppColors.surfaceVariant,
                    selectedColor: AppColors.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? AppColors.textOnPrimary : AppColors.textPrimary,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(MilkProduct product) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.spacing12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: _getCategoryGradient(product.category),
                  borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                ),
                child: const Icon(
                  Icons.local_drink,
                  color: AppColors.textOnPrimary,
                  size: AppConstants.iconLarge,
                ),
              ),
              const SizedBox(width: AppConstants.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: AppConstants.fontLarge,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacing4),
                    Text(
                      '${product.fatContent}% Fat • ${product.category.name}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontMedium,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacing4),
                    Text(
                      '₹${product.pricePerLiter.toStringAsFixed(2)}/L',
                      style: const TextStyle(
                        fontSize: AppConstants.fontLarge,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleProductAction(value, product),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: AppColors.error),
                        SizedBox(width: 8),
                        Text('Delete'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: AppConstants.spacing12),
          Text(
            product.description,
            style: const TextStyle(
              fontSize: AppConstants.fontMedium,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppConstants.spacing12),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.spacing12,
              vertical: AppConstants.spacing8,
            ),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
            ),
            child: const Text(
              'ACTIVE',
              style: TextStyle(
                color: AppColors.success,
                fontWeight: FontWeight.bold,
                fontSize: AppConstants.fontSmall,
              ),
            ),
          ),
        ],
      ),
    );
  }

  LinearGradient _getCategoryGradient(MilkCategory category) {
    switch (category) {
      case MilkCategory.fullCream:
        return AppColors.cardGradient1;
      case MilkCategory.toned:
        return AppColors.cardGradient2;
      case MilkCategory.doubleToned:
        return AppColors.cardGradient3;
      case MilkCategory.skimmed:
        return AppColors.cardGradient4;
      case MilkCategory.buffalo:
        return AppColors.primaryGradient;
      case MilkCategory.organic:
        return AppColors.secondaryGradient;
    }
  }

  void _handleProductAction(String action, MilkProduct product) {
    switch (action) {
      case 'edit':
        _showEditProductDialog(product);
        break;
      case 'delete':
        _showDeleteConfirmation(product);
        break;
    }
  }

  void _showAddProductDialog() {
    _showProductDialog();
  }

  void _showEditProductDialog(MilkProduct product) {
    _showProductDialog(product: product);
  }

  void _showProductDialog({MilkProduct? product}) {
    final isEditing = product != null;
    final nameController = TextEditingController(text: product?.name ?? '');
    final priceController = TextEditingController(
      text: product?.pricePerLiter.toString() ?? '',
    );
    final fatController = TextEditingController(
      text: product?.fatContent.toString() ?? '',
    );
    final descriptionController = TextEditingController(
      text: product?.description ?? '',
    );
    
    MilkCategory selectedCategory = product?.category ?? MilkCategory.fullCream;

    Get.dialog(
      AlertDialog(
        title: Text(isEditing ? 'Edit Product' : 'Add New Product'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Product Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppConstants.spacing16),
              DropdownButtonFormField<MilkCategory>(
                value: selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(),
                ),
                items: MilkCategory.values.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(category.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) selectedCategory = value;
                },
              ),
              const SizedBox(height: AppConstants.spacing16),
              TextFormField(
                controller: priceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Price per Liter (₹)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppConstants.spacing16),
              TextFormField(
                controller: fatController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Fat Content (%)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppConstants.spacing16),
              TextFormField(
                controller: descriptionController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty ||
                  priceController.text.trim().isEmpty ||
                  fatController.text.trim().isEmpty ||
                  descriptionController.text.trim().isEmpty) {
                Get.snackbar(
                  'Validation Error',
                  'Please fill all fields',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              final price = double.tryParse(priceController.text);
              final fat = double.tryParse(fatController.text);

              if (price == null || price <= 0) {
                Get.snackbar(
                  'Validation Error',
                  'Please enter a valid price',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              if (fat == null || fat < 0 || fat > 100) {
                Get.snackbar(
                  'Validation Error',
                  'Please enter a valid fat content (0-100%)',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              final newProduct = MilkProduct(
                id: product?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
                name: nameController.text.trim(),
                category: selectedCategory,
                pricePerLiter: price,
                fatContent: fat,
                description: descriptionController.text.trim(),
                createdAt: product?.createdAt ?? DateTime.now(),
              );

              bool success;
              if (isEditing) {
                success = await _productController.updateProduct(newProduct);
              } else {
                success = await _productController.addProduct(newProduct);
              }

              if (success) {
                Get.back();
              }
            },
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(MilkProduct product) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Product'),
        content: Text('Are you sure you want to delete "${product.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final success = await _productController.deleteProduct(product.id);
              if (success) {
                Get.back();
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
