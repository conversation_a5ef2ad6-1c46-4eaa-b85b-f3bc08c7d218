import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../models/payment.dart';
import '../../controllers/payment_controller.dart';

class PaymentManagementPage extends StatefulWidget {
  const PaymentManagementPage({super.key});

  @override
  State<PaymentManagementPage> createState() => _PaymentManagementPageState();
}

class _PaymentManagementPageState extends State<PaymentManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final PaymentController _paymentController = Get.put(PaymentController());

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Management'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All Payments', icon: Icon(Icons.payment)),
            Tab(text: 'Cash', icon: Icon(Icons.money)),
            Tab(text: 'Online', icon: Icon(Icons.credit_card)),
            Tab(text: 'UPI', icon: Icon(Icons.qr_code)),
          ],
        ),
      ),
      body: Column(
        children: [
          // Payment Summary
          _buildPaymentSummary(),
          
          // Payment List
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                _AllPayments(),
                _CashPayments(),
                _OnlinePayments(),
                _UPIPayments(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSummary() {
    return Obx(() {
      final todaysCollection = _paymentController.getTodaysCollection();
      final monthlyCollection = _paymentController.getMonthlyCollection();
      final cashTotal = _paymentController.getCashCollectionTotal();

      return Container(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        decoration: BoxDecoration(
          gradient: AppColors.primaryGradient,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Today\'s Collection',
                    '₹${todaysCollection.toStringAsFixed(0)}',
                    Icons.today,
                  ),
                ),
                const SizedBox(width: AppConstants.spacing12),
                Expanded(
                  child: _buildSummaryCard(
                    'Monthly Collection',
                    '₹${monthlyCollection.toStringAsFixed(0)}',
                    Icons.calendar_month,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.spacing12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Cash Collection',
                    '₹${cashTotal.toStringAsFixed(0)}',
                    Icons.money,
                  ),
                ),
                const SizedBox(width: AppConstants.spacing12),
                Expanded(
                  child: _buildSummaryCard(
                    'Total Payments',
                    '${_paymentController.payments.length}',
                    Icons.receipt_long,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildSummaryCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacing12),
      decoration: BoxDecoration(
        color: AppColors.surface.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppColors.primary, size: AppConstants.iconMedium),
          const SizedBox(height: AppConstants.spacing8),
          Text(
            value,
            style: const TextStyle(
              fontSize: AppConstants.fontLarge,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: AppConstants.fontSmall,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _AllPayments extends StatelessWidget {
  const _AllPayments();

  @override
  Widget build(BuildContext context) {
    final PaymentController paymentController = Get.find<PaymentController>();

    return Obx(() {
      if (paymentController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final payments = paymentController.payments;

      if (payments.isEmpty) {
        return const EmptyState(
          icon: Icons.payment_outlined,
          title: 'No Payments Found',
          subtitle: 'Payment records will appear here.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: payments.length,
        itemBuilder: (context, index) {
          final payment = payments[index];
          return _buildPaymentCard(payment);
        },
      );
    });
  }
}

class _CashPayments extends StatelessWidget {
  const _CashPayments();

  @override
  Widget build(BuildContext context) {
    final PaymentController paymentController = Get.find<PaymentController>();

    return Obx(() {
      final cashPayments = paymentController.getCashPayments();

      if (cashPayments.isEmpty) {
        return const EmptyState(
          icon: Icons.money_off,
          title: 'No Cash Payments',
          subtitle: 'Cash payment records will appear here.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: cashPayments.length,
        itemBuilder: (context, index) {
          final payment = cashPayments[index];
          return _buildPaymentCard(payment);
        },
      );
    });
  }
}

class _OnlinePayments extends StatelessWidget {
  const _OnlinePayments();

  @override
  Widget build(BuildContext context) {
    final PaymentController paymentController = Get.find<PaymentController>();

    return Obx(() {
      final onlinePayments = paymentController.payments
          .where((p) => p.method == PaymentMethod.online)
          .toList();

      if (onlinePayments.isEmpty) {
        return const EmptyState(
          icon: Icons.credit_card_off,
          title: 'No Online Payments',
          subtitle: 'Online payment records will appear here.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: onlinePayments.length,
        itemBuilder: (context, index) {
          final payment = onlinePayments[index];
          return _buildPaymentCard(payment);
        },
      );
    });
  }
}

class _UPIPayments extends StatelessWidget {
  const _UPIPayments();

  @override
  Widget build(BuildContext context) {
    final PaymentController paymentController = Get.find<PaymentController>();

    return Obx(() {
      final upiPayments = paymentController.payments
          .where((p) => p.method == PaymentMethod.upi)
          .toList();

      if (upiPayments.isEmpty) {
        return const EmptyState(
          icon: Icons.qr_code_scanner_outlined,
          title: 'No UPI Payments',
          subtitle: 'UPI payment records will appear here.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppConstants.spacing16),
        itemCount: upiPayments.length,
        itemBuilder: (context, index) {
          final payment = upiPayments[index];
          return _buildPaymentCard(payment);
        },
      );
    });
  }
}

Widget _buildPaymentCard(Payment payment) {
  return CustomCard(
    margin: const EdgeInsets.only(bottom: AppConstants.spacing12),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: _getPaymentMethodColor(payment.method).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                _getPaymentMethodIcon(payment.method),
                color: _getPaymentMethodColor(payment.method),
                size: AppConstants.iconMedium,
              ),
            ),
            const SizedBox(width: AppConstants.spacing16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    payment.customerName,
                    style: const TextStyle(
                      fontSize: AppConstants.fontLarge,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: AppConstants.spacing4),
                  Text(
                    'Order #${payment.orderId.substring(0, 8)}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontMedium,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: AppConstants.spacing4),
                  Text(
                    payment.method.name.toUpperCase(),
                    style: TextStyle(
                      fontSize: AppConstants.fontSmall,
                      fontWeight: FontWeight.w600,
                      color: _getPaymentMethodColor(payment.method),
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '₹${payment.amount.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontXLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.success,
                  ),
                ),
                Text(
                  _formatDate(payment.paymentDate),
                  style: const TextStyle(
                    fontSize: AppConstants.fontSmall,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
        
        if (payment.transactionId != null) ...[
          const SizedBox(height: AppConstants.spacing12),
          Container(
            padding: const EdgeInsets.all(AppConstants.spacing8),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.receipt,
                  size: AppConstants.iconSmall,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: AppConstants.spacing8),
                Text(
                  'Transaction ID: ${payment.transactionId}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSmall,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
        
        if (payment.notes != null && payment.notes!.isNotEmpty) ...[
          const SizedBox(height: AppConstants.spacing12),
          Container(
            padding: const EdgeInsets.all(AppConstants.spacing8),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.note,
                  size: AppConstants.iconSmall,
                  color: AppColors.info,
                ),
                const SizedBox(width: AppConstants.spacing8),
                Expanded(
                  child: Text(
                    payment.notes!,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSmall,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
        
        if (payment.fieldManName != null) ...[
          const SizedBox(height: AppConstants.spacing8),
          Row(
            children: [
              const Icon(
                Icons.person,
                size: AppConstants.iconSmall,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: AppConstants.spacing8),
              Text(
                'Collected by: ${payment.fieldManName}',
                style: const TextStyle(
                  fontSize: AppConstants.fontSmall,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ],
    ),
  );
}

IconData _getPaymentMethodIcon(PaymentMethod method) {
  switch (method) {
    case PaymentMethod.cash:
      return Icons.money;
    case PaymentMethod.online:
      return Icons.credit_card;
    case PaymentMethod.upi:
      return Icons.qr_code;
    case PaymentMethod.cheque:
      return Icons.receipt;
  }
}

Color _getPaymentMethodColor(PaymentMethod method) {
  switch (method) {
    case PaymentMethod.cash:
      return AppColors.success;
    case PaymentMethod.online:
      return AppColors.primary;
    case PaymentMethod.upi:
      return AppColors.secondary;
    case PaymentMethod.cheque:
      return AppColors.warning;
  }
}

String _formatDate(DateTime date) {
  return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
}
