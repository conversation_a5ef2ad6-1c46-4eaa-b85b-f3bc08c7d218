import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../../models/notification.dart';
import '../../services/database_service.dart';

class NotificationController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();
  
  // Observables
  final RxList<AppNotification> _notifications = <AppNotification>[].obs;
  final RxBool _isLoading = false.obs;
  final RxInt _unreadCount = 0.obs;
  final RxBool _notificationsEnabled = true.obs;
  
  // Getters
  List<AppNotification> get notifications => _notifications;
  bool get isLoading => _isLoading.value;
  int get unreadCount => _unreadCount.value;
  bool get notificationsEnabled => _notificationsEnabled.value;
  
  @override
  void onInit() {
    super.onInit();
    loadNotifications();
    _startPeriodicChecks();
  }
  
  Future<void> loadNotifications() async {
    try {
      _isLoading.value = true;
      final notificationList = await _databaseService.getNotifications();
      _notifications.assignAll(notificationList);
      _updateUnreadCount();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load notifications',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<void> addNotification(AppNotification notification) async {
    try {
      await _databaseService.insertNotification(notification);
      _notifications.insert(0, notification);
      _updateUnreadCount();
      
      // Show in-app notification if enabled
      if (_notificationsEnabled.value) {
        _showInAppNotification(notification);
      }
    } catch (e) {
      print('Failed to add notification: $e');
    }
  }
  
  Future<void> markAsRead(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1 && !_notifications[index].isRead) {
        final updatedNotification = _notifications[index].copyWith(
          isRead: true,
          readAt: DateTime.now(),
        );
        _notifications[index] = updatedNotification;
        _updateUnreadCount();
      }
    } catch (e) {
      print('Failed to mark notification as read: $e');
    }
  }
  
  Future<void> markAllAsRead() async {
    try {
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
      }
      _updateUnreadCount();
      
      Get.snackbar(
        'Success',
        'All notifications marked as read',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to mark notifications as read',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
  
  Future<void> deleteNotification(String notificationId) async {
    try {
      _notifications.removeWhere((n) => n.id == notificationId);
      _updateUnreadCount();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to delete notification',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
  
  Future<void> clearAllNotifications() async {
    try {
      _notifications.clear();
      _updateUnreadCount();
      
      Get.snackbar(
        'Success',
        'All notifications cleared',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to clear notifications',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
  
  void toggleNotifications() {
    _notificationsEnabled.value = !_notificationsEnabled.value;
    
    Get.snackbar(
      'Notifications',
      _notificationsEnabled.value ? 'Notifications enabled' : 'Notifications disabled',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
  
  void _updateUnreadCount() {
    _unreadCount.value = _notifications.where((n) => !n.isRead).length;
  }
  
  void _showInAppNotification(AppNotification notification) {
    Get.snackbar(
      notification.title,
      notification.body,
      snackPosition: SnackPosition.TOP,
      backgroundColor: _getNotificationColor(notification.type),
      colorText: Colors.white,
      icon: Icon(
        _getNotificationIcon(notification.type),
        color: Colors.white,
      ),
      duration: const Duration(seconds: 4),
      isDismissible: true,
      dismissDirection: DismissDirection.horizontal,
      onTap: (_) => markAsRead(notification.id),
    );
  }
  
  void _startPeriodicChecks() {
    // Check for new notifications every 30 seconds
    Stream.periodic(const Duration(seconds: 30)).listen((_) {
      _checkForNewNotifications();
    });
  }
  
  Future<void> _checkForNewNotifications() async {
    // This would typically check with a server for new notifications
    // For now, we'll simulate some automatic notifications
    _checkPaymentReminders();
    _checkLowStockAlerts();
    _checkOrderUpdates();
  }
  
  void _checkPaymentReminders() {
    // Simulate payment reminder logic
    final now = DateTime.now();
    if (now.hour == 9 && now.minute == 0) { // 9 AM daily
      createPaymentReminderNotification();
    }
  }
  
  void _checkLowStockAlerts() {
    // Simulate low stock alerts
    // This would check actual inventory levels
  }
  
  void _checkOrderUpdates() {
    // Simulate order status updates
    // This would check for actual order changes
  }
  
  // Notification creation methods
  Future<void> createOrderNotification({
    required String orderId,
    required String customerName,
    required String status,
    String? additionalInfo,
  }) async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Order Update',
      body: 'Order #${orderId.substring(0, 8)} for $customerName is now $status',
      type: NotificationType.orderUpdate,
      data: {
        'orderId': orderId,
        'customerName': customerName,
        'status': status,
        'additionalInfo': additionalInfo,
      },
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }
  
  Future<void> createPaymentNotification({
    required String orderId,
    required String customerName,
    required double amount,
    required String method,
  }) async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Payment Received',
      body: 'Payment of ₹${amount.toStringAsFixed(2)} received from $customerName via $method',
      type: NotificationType.paymentReceived,
      data: {
        'orderId': orderId,
        'customerName': customerName,
        'amount': amount.toString(),
        'method': method,
      },
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }
  
  Future<void> createPaymentReminderNotification() async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Payment Reminder',
      body: 'You have pending payments to collect today',
      type: NotificationType.paymentReminder,
      data: {},
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }
  
  Future<void> createLowStockNotification({
    required String productName,
    required int currentStock,
    required int minimumStock,
  }) async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Low Stock Alert',
      body: '$productName is running low (${currentStock} remaining)',
      type: NotificationType.lowStock,
      data: {
        'productName': productName,
        'currentStock': currentStock.toString(),
        'minimumStock': minimumStock.toString(),
      },
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }
  
  Future<void> createDeliveryNotification({
    required String orderId,
    required String customerName,
    required String status,
  }) async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Delivery Update',
      body: 'Delivery for order #${orderId.substring(0, 8)} ($customerName) is $status',
      type: NotificationType.deliveryUpdate,
      data: {
        'orderId': orderId,
        'customerName': customerName,
        'status': status,
      },
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }
  
  Future<void> createSystemNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      body: body,
      type: NotificationType.system,
      data: data ?? {},
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }
  
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.orderUpdate:
        return Colors.blue;
      case NotificationType.paymentReceived:
        return Colors.green;
      case NotificationType.paymentReminder:
        return Colors.orange;
      case NotificationType.lowStock:
        return Colors.red;
      case NotificationType.deliveryUpdate:
        return Colors.purple;
      case NotificationType.system:
        return Colors.grey;
    }
  }
  
  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.orderUpdate:
        return Icons.shopping_cart;
      case NotificationType.paymentReceived:
        return Icons.payment;
      case NotificationType.paymentReminder:
        return Icons.schedule;
      case NotificationType.lowStock:
        return Icons.warning;
      case NotificationType.deliveryUpdate:
        return Icons.local_shipping;
      case NotificationType.system:
        return Icons.info;
    }
  }
  
  List<AppNotification> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }
  
  List<AppNotification> getUnreadNotifications() {
    return _notifications.where((n) => !n.isRead).toList();
  }
  
  List<AppNotification> getTodaysNotifications() {
    final today = DateTime.now();
    return _notifications.where((n) {
      return n.createdAt.year == today.year &&
             n.createdAt.month == today.month &&
             n.createdAt.day == today.day;
    }).toList();
  }
}
