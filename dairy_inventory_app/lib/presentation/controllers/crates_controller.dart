import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../../models/crate.dart';
import '../../models/analytics.dart';
import '../../services/database_service.dart';
import '../../core/constants/app_colors.dart';

class CratesController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();

  // Observables
  final RxList<Crate> _crates = <Crate>[].obs;
  final RxList<Crate> _filteredCrates = <Crate>[].obs;
  final RxList<CrateMovement> _movements = <CrateMovement>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedCondition = 'all'.obs;
  final RxString _selectedType = 'all'.obs;
  final RxString _selectedSize = 'all'.obs;
  final RxString _sortBy = 'name'.obs;
  final RxBool _sortAscending = true.obs;
  final Rx<DateRange?> _dateRange = Rx<DateRange?>(null);

  // Getters
  List<Crate> get crates => _crates;
  List<Crate> get filteredCrates => _filteredCrates;
  List<CrateMovement> get movements => _movements;
  bool get isLoading => _isLoading.value;
  String get searchQuery => _searchQuery.value;
  String get selectedCondition => _selectedCondition.value;
  String get selectedType => _selectedType.value;
  String get selectedSize => _selectedSize.value;
  String get sortBy => _sortBy.value;
  bool get sortAscending => _sortAscending.value;
  DateRange? get dateRange => _dateRange.value;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    try {
      _isLoading.value = true;
      await Future.wait([loadCrates(), loadMovements()]);
      _applyFilters();
    } catch (e) {
      debugPrint('Error loading initial data: $e');
      Get.snackbar(
        'Error',
        'Failed to load crates data',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadCrates() async {
    try {
      final crateList = await _databaseService.getCrates();
      _crates.assignAll(crateList);
    } catch (e) {
      debugPrint('Error loading crates: $e');
      rethrow;
    }
  }

  Future<void> loadMovements() async {
    try {
      final movementList = await _databaseService.getCrateMovements();
      _movements.assignAll(movementList);
    } catch (e) {
      debugPrint('Error loading movements: $e');
      rethrow;
    }
  }

  // CRUD Operations
  Future<bool> createCrate(Crate crate) async {
    try {
      _isLoading.value = true;
      await _databaseService.insertCrate(crate);
      _crates.insert(0, crate);
      _applyFilters();

      Get.snackbar(
        'Success',
        'Crate created successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );
      return true;
    } catch (e) {
      debugPrint('Error creating crate: $e');
      Get.snackbar(
        'Error',
        'Failed to create crate',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateCrate(Crate crate) async {
    try {
      _isLoading.value = true;
      await _databaseService.updateCrate(crate);

      final index = _crates.indexWhere((c) => c.id == crate.id);
      if (index != -1) {
        _crates[index] = crate;
        _applyFilters();
      }

      Get.snackbar(
        'Success',
        'Crate updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );
      return true;
    } catch (e) {
      debugPrint('Error updating crate: $e');
      Get.snackbar(
        'Error',
        'Failed to update crate',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deleteCrate(String crateId) async {
    try {
      _isLoading.value = true;
      await _databaseService.deleteCrate(crateId);
      _crates.removeWhere((crate) => crate.id == crateId);
      _applyFilters();

      Get.snackbar(
        'Success',
        'Crate deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );
      return true;
    } catch (e) {
      debugPrint('Error deleting crate: $e');
      Get.snackbar(
        'Error',
        'Failed to delete crate',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Stock Management
  Future<bool> updateStock(
    String crateId,
    int newStock,
    CrateMovementType movementType, {
    String? notes,
  }) async {
    try {
      final crate = _crates.firstWhere((c) => c.id == crateId);
      final stockDifference = newStock - crate.currentStock;

      // Create movement record
      final movement = CrateMovement(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        crateId: crateId,
        type: movementType,
        quantity: stockDifference.abs(),
        notes: notes,
        movementDate: DateTime.now(),
        recordedBy: 'admin', // TODO: Get current user
        createdAt: DateTime.now(),
      );

      // Update crate stock
      final updatedCrate = crate.copyWith(
        currentStock: newStock,
        updatedAt: DateTime.now(),
      );

      await _databaseService.insertCrateMovement(movement);
      await updateCrate(updatedCrate);
      _movements.insert(0, movement);

      return true;
    } catch (e) {
      debugPrint('Error updating stock: $e');
      return false;
    }
  }

  Future<bool> recordMovement(CrateMovement movement) async {
    try {
      await _databaseService.insertCrateMovement(movement);
      _movements.insert(0, movement);

      // Update crate stock based on movement
      final crate = _crates.firstWhere((c) => c.id == movement.crateId);
      int newStock = crate.currentStock;

      switch (movement.type) {
        case CrateMovementType.incoming:
        case CrateMovementType.returned:
          newStock += movement.quantity;
          break;
        case CrateMovementType.outgoing:
        case CrateMovementType.damaged:
        case CrateMovementType.lost:
          newStock -= movement.quantity;
          break;
        case CrateMovementType.maintenance:
        case CrateMovementType.disposed:
          // These don't affect stock directly
          break;
      }

      final updatedCrate = crate.copyWith(
        currentStock: newStock,
        updatedAt: DateTime.now(),
      );

      await updateCrate(updatedCrate);
      return true;
    } catch (e) {
      debugPrint('Error recording movement: $e');
      return false;
    }
  }

  // Filtering and Searching
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  void setConditionFilter(String condition) {
    _selectedCondition.value = condition;
    _applyFilters();
  }

  void setTypeFilter(String type) {
    _selectedType.value = type;
    _applyFilters();
  }

  void setSizeFilter(String size) {
    _selectedSize.value = size;
    _applyFilters();
  }

  void setDateRange(DateRange? range) {
    _dateRange.value = range;
    _applyFilters();
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedCondition.value = 'all';
    _selectedType.value = 'all';
    _selectedSize.value = 'all';
    _dateRange.value = null;
    _applyFilters();
  }

  void _applyFilters() {
    List<Crate> filtered = List.from(_crates);

    // Search filter
    if (_searchQuery.value.isNotEmpty) {
      filtered =
          filtered.where((crate) {
            return crate.name.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                crate.id.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                crate.serialNumber?.toLowerCase().contains(
                      _searchQuery.value.toLowerCase(),
                    ) ==
                    true;
          }).toList();
    }

    // Condition filter
    if (_selectedCondition.value != 'all') {
      final condition = CrateCondition.values.firstWhere(
        (c) => c.name == _selectedCondition.value,
        orElse: () => CrateCondition.good,
      );
      filtered =
          filtered.where((crate) => crate.condition == condition).toList();
    }

    // Type filter
    if (_selectedType.value != 'all') {
      final type = CrateType.values.firstWhere(
        (t) => t.name == _selectedType.value,
        orElse: () => CrateType.plastic,
      );
      filtered = filtered.where((crate) => crate.type == type).toList();
    }

    // Size filter
    if (_selectedSize.value != 'all') {
      final size = CrateSize.values.firstWhere(
        (s) => s.name == _selectedSize.value,
        orElse: () => CrateSize.medium,
      );
      filtered = filtered.where((crate) => crate.size == size).toList();
    }

    // Apply sorting
    _applySorting(filtered);
  }

  // Sorting
  void setSorting(String sortField, bool ascending) {
    _sortBy.value = sortField;
    _sortAscending.value = ascending;
    _applyFilters();
  }

  void _applySorting(List<Crate> crates) {
    crates.sort((a, b) {
      int comparison = 0;

      switch (_sortBy.value) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'currentStock':
          comparison = a.currentStock.compareTo(b.currentStock);
          break;
        case 'condition':
          comparison = a.condition.index.compareTo(b.condition.index);
          break;
        case 'type':
          comparison = a.type.index.compareTo(b.type.index);
          break;
        case 'size':
          comparison = a.size.index.compareTo(b.size.index);
          break;
        case 'purchaseDate':
          comparison = a.purchaseDate.compareTo(b.purchaseDate);
          break;
        default:
          comparison = a.name.compareTo(b.name);
      }

      return _sortAscending.value ? comparison : -comparison;
    });

    _filteredCrates.assignAll(crates);
  }

  // Analytics and Reporting
  CrateAnalytics getCrateAnalytics({DateRange? range}) {
    final analysisRange = range ?? DateRange.thisMonth();

    final totalCrates = _crates.length;
    final availableCrates = _crates.where((c) => c.canBeUsed()).length;
    final cratesInUse =
        _crates.where((c) => c.currentStock < c.maximumStock).length;
    final damagedCrates =
        _crates.where((c) => c.condition == CrateCondition.damaged).length;
    final lostCrates =
        _crates.where((c) => c.condition == CrateCondition.lost).length;

    // Group by condition
    final Map<String, int> cratesByCondition = {};
    for (final condition in CrateCondition.values) {
      cratesByCondition[condition.name] =
          _crates.where((c) => c.condition == condition).length;
    }

    // Group by type
    final Map<String, int> cratesByType = {};
    for (final type in CrateType.values) {
      cratesByType[type.name] = _crates.where((c) => c.type == type).length;
    }

    // Group by size
    final Map<String, int> cratesBySize = {};
    for (final size in CrateSize.values) {
      cratesBySize[size.name] = _crates.where((c) => c.size == size).length;
    }

    // Low stock crates
    final lowStockCrates =
        _crates
            .where((c) => c.isLowStock)
            .map(
              (c) => {
                'id': c.id,
                'name': c.name,
                'currentStock': c.currentStock,
                'minimumStock': c.minimumStock,
              },
            )
            .toList();

    // Maintenance required
    final maintenanceRequired =
        _crates
            .where((c) => c.needsMaintenance)
            .map(
              (c) => {
                'id': c.id,
                'name': c.name,
                'nextMaintenanceDate': c.nextMaintenanceDate?.toIso8601String(),
                'daysUntilMaintenance': c.getDaysUntilMaintenance(),
              },
            )
            .toList();

    final totalCrateValue = _crates.fold(
      0.0,
      (sum, crate) => sum + crate.purchasePrice,
    );

    // Today's movements
    final today = DateTime.now();
    final todaysMovements =
        _movements
            .where(
              (m) =>
                  m.movementDate.year == today.year &&
                  m.movementDate.month == today.month &&
                  m.movementDate.day == today.day,
            )
            .toList();

    final cratesDeliveredToday = todaysMovements
        .where((m) => m.type == CrateMovementType.outgoing)
        .fold(0, (sum, m) => sum + m.quantity);

    final cratesReturnedToday = todaysMovements
        .where((m) => m.type == CrateMovementType.returned)
        .fold(0, (sum, m) => sum + m.quantity);

    return CrateAnalytics(
      totalCrates: totalCrates,
      availableCrates: availableCrates,
      cratesInUse: cratesInUse,
      damagedCrates: damagedCrates,
      lostCrates: lostCrates,
      cratesByCondition: cratesByCondition,
      cratesByType: cratesByType,
      cratesBySize: cratesBySize,
      lowStockCrates: lowStockCrates,
      maintenanceRequired: maintenanceRequired,
      totalCrateValue: totalCrateValue,
      cratesDeliveredToday: cratesDeliveredToday,
      cratesReturnedToday: cratesReturnedToday,
      dateRange: analysisRange,
    );
  }

  // Quick access methods
  List<Crate> getLowStockCrates() {
    return _crates.where((crate) => crate.isLowStock).toList();
  }

  List<Crate> getDamagedCrates() {
    return _crates
        .where((crate) => crate.condition == CrateCondition.damaged)
        .toList();
  }

  List<Crate> getMaintenanceRequiredCrates() {
    return _crates.where((crate) => crate.needsMaintenance).toList();
  }

  List<Crate> getAvailableCrates() {
    return _crates.where((crate) => crate.canBeUsed()).toList();
  }

  List<CrateMovement> getTodaysMovements() {
    final today = DateTime.now();
    return _movements.where((movement) {
      return movement.movementDate.year == today.year &&
          movement.movementDate.month == today.month &&
          movement.movementDate.day == today.day;
    }).toList();
  }

  List<CrateMovement> getMovementsByType(CrateMovementType type) {
    return _movements.where((movement) => movement.type == type).toList();
  }

  List<CrateMovement> getMovementsByCrate(String crateId) {
    return _movements.where((movement) => movement.crateId == crateId).toList();
  }

  // Utility methods
  int getTotalStock() {
    return _crates.fold(0, (sum, crate) => sum + crate.currentStock);
  }

  int getAvailableStock() {
    return _crates.fold(0, (sum, crate) => sum + crate.availableStock);
  }

  double getTotalValue() {
    return _crates.fold(0.0, (sum, crate) => sum + crate.purchasePrice);
  }

  Map<String, int> getStockByType() {
    final Map<String, int> stockByType = {};
    for (final type in CrateType.values) {
      stockByType[type.name] = _crates
          .where((c) => c.type == type)
          .fold(0, (sum, c) => sum + c.currentStock);
    }
    return stockByType;
  }

  Map<String, int> getStockBySize() {
    final Map<String, int> stockBySize = {};
    for (final size in CrateSize.values) {
      stockBySize[size.name] = _crates
          .where((c) => c.size == size)
          .fold(0, (sum, c) => sum + c.currentStock);
    }
    return stockBySize;
  }

  Map<String, int> getStockByCondition() {
    final Map<String, int> stockByCondition = {};
    for (final condition in CrateCondition.values) {
      stockByCondition[condition.name] = _crates
          .where((c) => c.condition == condition)
          .fold(0, (sum, c) => sum + c.currentStock);
    }
    return stockByCondition;
  }

  // Alerts and Notifications
  List<Map<String, dynamic>> getAlerts() {
    final List<Map<String, dynamic>> alerts = [];

    // Low stock alerts
    final lowStockCrates = getLowStockCrates();
    if (lowStockCrates.isNotEmpty) {
      alerts.add({
        'type': 'low_stock',
        'title': 'Low Stock Alert',
        'message': '${lowStockCrates.length} crate(s) are running low on stock',
        'severity': 'warning',
        'count': lowStockCrates.length,
      });
    }

    // Maintenance alerts
    final maintenanceRequired = getMaintenanceRequiredCrates();
    if (maintenanceRequired.isNotEmpty) {
      alerts.add({
        'type': 'maintenance',
        'title': 'Maintenance Required',
        'message': '${maintenanceRequired.length} crate(s) require maintenance',
        'severity': 'warning',
        'count': maintenanceRequired.length,
      });
    }

    // Damaged crates alerts
    final damagedCrates = getDamagedCrates();
    if (damagedCrates.isNotEmpty) {
      alerts.add({
        'type': 'damaged',
        'title': 'Damaged Crates',
        'message':
            '${damagedCrates.length} crate(s) are damaged and need attention',
        'severity': 'error',
        'count': damagedCrates.length,
      });
    }

    return alerts;
  }

  // Bulk Operations
  Future<bool> bulkUpdateCondition(
    List<String> crateIds,
    CrateCondition condition,
  ) async {
    try {
      _isLoading.value = true;
      int successCount = 0;

      for (String crateId in crateIds) {
        final crate = _crates.firstWhere((c) => c.id == crateId);
        final updatedCrate = crate.copyWith(
          condition: condition,
          updatedAt: DateTime.now(),
        );

        final success = await updateCrate(updatedCrate);
        if (success) successCount++;
      }

      Get.snackbar(
        'Bulk Update',
        'Updated $successCount of ${crateIds.length} crates',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor:
            successCount == crateIds.length
                ? AppColors.success
                : AppColors.warning,
        colorText: AppColors.textOnPrimary,
      );

      return successCount > 0;
    } catch (e) {
      debugPrint('Error in bulk update: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> bulkUpdateStock(
    List<String> crateIds,
    int stockChange,
    CrateMovementType movementType,
  ) async {
    try {
      _isLoading.value = true;
      int successCount = 0;

      for (String crateId in crateIds) {
        final crate = _crates.firstWhere((c) => c.id == crateId);
        final newStock = crate.currentStock + stockChange;

        final success = await updateStock(crateId, newStock, movementType);
        if (success) successCount++;
      }

      Get.snackbar(
        'Bulk Stock Update',
        'Updated stock for $successCount of ${crateIds.length} crates',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor:
            successCount == crateIds.length
                ? AppColors.success
                : AppColors.warning,
        colorText: AppColors.textOnPrimary,
      );

      return successCount > 0;
    } catch (e) {
      debugPrint('Error in bulk stock update: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
}
