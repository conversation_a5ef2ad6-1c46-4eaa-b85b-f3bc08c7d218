import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../../models/billing.dart';
import '../../models/order.dart';
import '../../models/user.dart';
import '../../models/analytics.dart';
import '../../services/database_service.dart';
import '../../core/constants/app_colors.dart';

class BillingController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();

  // Observables
  final RxList<Bill> _bills = <Bill>[].obs;
  final RxList<Bill> _filteredBills = <Bill>[].obs;
  final RxList<User> _customers = <User>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedStatus = 'all'.obs;
  final RxString _selectedCustomer = 'all'.obs;
  final RxString _sortBy = 'billDate'.obs;
  final RxBool _sortAscending = false.obs;
  final Rx<DateRange?> _dateRange = Rx<DateRange?>(null);

  // Getters
  List<Bill> get bills => _bills;
  List<Bill> get filteredBills => _filteredBills;
  List<User> get customers => _customers;
  bool get isLoading => _isLoading.value;
  String get searchQuery => _searchQuery.value;
  String get selectedStatus => _selectedStatus.value;
  String get selectedCustomer => _selectedCustomer.value;
  String get sortBy => _sortBy.value;
  bool get sortAscending => _sortAscending.value;
  DateRange? get dateRange => _dateRange.value;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    try {
      _isLoading.value = true;
      await Future.wait([loadBills(), loadCustomers()]);
      _applyFilters();
    } catch (e) {
      debugPrint('Error loading initial data: $e');
      Get.snackbar(
        'Error',
        'Failed to load billing data',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadBills() async {
    try {
      final billList = await _databaseService.getBills();
      _bills.assignAll(billList);
    } catch (e) {
      debugPrint('Error loading bills: $e');
      rethrow;
    }
  }

  Future<void> loadCustomers() async {
    try {
      final users = await _databaseService.getUsers();
      _customers.assignAll(
        users.where((user) => user.role == UserRole.customer).toList(),
      );
    } catch (e) {
      debugPrint('Error loading customers: $e');
      rethrow;
    }
  }

  // Bill Generation
  Future<Bill?> generateDailyBill(String customerId, DateTime date) async {
    try {
      _isLoading.value = true;

      // Get customer details
      final customer = _customers.firstWhere((c) => c.id == customerId);

      // Get orders for the specific date
      final orders = await _databaseService.getOrders();
      final dailyOrders =
          orders
              .where(
                (order) =>
                    order.customerId == customerId &&
                    order.orderDate.year == date.year &&
                    order.orderDate.month == date.month &&
                    order.orderDate.day == date.day &&
                    order.status == OrderStatus.delivered,
              )
              .toList();

      if (dailyOrders.isEmpty) {
        Get.snackbar(
          'Info',
          'No delivered orders found for this date',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.info,
          colorText: AppColors.textOnPrimary,
        );
        return null;
      }

      // Create bill items from orders
      final List<BillItem> billItems = [];
      for (final order in dailyOrders) {
        for (final item in order.items) {
          billItems.add(
            BillItem(
              productId: item.productId,
              productName: item.productName,
              productCategory: item.productCategory,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalAmount: item.totalPrice,
              discountAmount: item.discountAmount,
              orderDate: order.orderDate,
              orderId: order.id,
            ),
          );
        }
      }

      // Calculate totals
      final subtotal = billItems.fold(
        0.0,
        (sum, item) => sum + item.totalAmount,
      );
      final totalDiscount = billItems.fold(
        0.0,
        (sum, item) => sum + (item.discountAmount ?? 0.0),
      );
      final totalAmount = subtotal - totalDiscount;

      // Create bill
      final bill = Bill(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        customerId: customerId,
        customerName: customer.name,
        customerEmail: customer.email,
        customerPhone: customer.phone,
        customerAddress: customer.address,
        billDate: DateTime.now(),
        fromDate: date,
        toDate: date,
        items: billItems,
        subtotal: subtotal,
        discountAmount: totalDiscount,
        totalAmount: totalAmount,
        pendingAmount: totalAmount,
        status: BillStatus.generated,
        generatedBy: 'admin', // TODO: Get current user
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.insertBill(bill);
      _bills.insert(0, bill);
      _applyFilters();

      Get.snackbar(
        'Success',
        'Daily bill generated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return bill;
    } catch (e) {
      debugPrint('Error generating daily bill: $e');
      Get.snackbar(
        'Error',
        'Failed to generate bill',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<Bill?> generateMonthlyBill(String customerId, DateTime month) async {
    try {
      _isLoading.value = true;

      final customer = _customers.firstWhere((c) => c.id == customerId);

      // Get orders for the entire month
      final orders = await _databaseService.getOrders();
      final monthlyOrders =
          orders
              .where(
                (order) =>
                    order.customerId == customerId &&
                    order.orderDate.year == month.year &&
                    order.orderDate.month == month.month &&
                    order.status == OrderStatus.delivered,
              )
              .toList();

      if (monthlyOrders.isEmpty) {
        Get.snackbar(
          'Info',
          'No delivered orders found for this month',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.info,
          colorText: AppColors.textOnPrimary,
        );
        return null;
      }

      // Create consolidated bill items
      final Map<String, BillItem> consolidatedItems = {};

      for (final order in monthlyOrders) {
        for (final item in order.items) {
          final key = '${item.productId}_${item.unitPrice}';

          if (consolidatedItems.containsKey(key)) {
            final existing = consolidatedItems[key]!;
            consolidatedItems[key] = BillItem(
              productId: existing.productId,
              productName: existing.productName,
              productCategory: existing.productCategory,
              quantity: existing.quantity + item.quantity,
              unitPrice: existing.unitPrice,
              totalAmount: existing.totalAmount + item.totalPrice,
              discountAmount:
                  (existing.discountAmount ?? 0.0) +
                  (item.discountAmount ?? 0.0),
              orderDate: existing.orderDate,
              orderId: existing.orderId,
            );
          } else {
            consolidatedItems[key] = BillItem(
              productId: item.productId,
              productName: item.productName,
              productCategory: item.productCategory,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalAmount: item.totalPrice,
              discountAmount: item.discountAmount,
              orderDate: order.orderDate,
              orderId: order.id,
            );
          }
        }
      }

      final billItems = consolidatedItems.values.toList();
      final subtotal = billItems.fold(
        0.0,
        (sum, item) => sum + item.totalAmount,
      );
      final totalDiscount = billItems.fold(
        0.0,
        (sum, item) => sum + (item.discountAmount ?? 0.0),
      );
      final totalAmount = subtotal - totalDiscount;

      final firstDay = DateTime(month.year, month.month, 1);
      final lastDay = DateTime(month.year, month.month + 1, 0);

      final bill = Bill(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        customerId: customerId,
        customerName: customer.name,
        customerEmail: customer.email,
        customerPhone: customer.phone,
        customerAddress: customer.address,
        billDate: DateTime.now(),
        fromDate: firstDay,
        toDate: lastDay,
        items: billItems,
        subtotal: subtotal,
        discountAmount: totalDiscount,
        totalAmount: totalAmount,
        pendingAmount: totalAmount,
        status: BillStatus.generated,
        dueDate: DateTime.now().add(const Duration(days: 30)),
        generatedBy: 'admin',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.insertBill(bill);
      _bills.insert(0, bill);
      _applyFilters();

      Get.snackbar(
        'Success',
        'Monthly bill generated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return bill;
    } catch (e) {
      debugPrint('Error generating monthly bill: $e');
      Get.snackbar(
        'Error',
        'Failed to generate monthly bill',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  // Bill Management
  Future<bool> updateBillStatus(String billId, BillStatus status) async {
    try {
      final bill = _bills.firstWhere((b) => b.id == billId);
      final updatedBill = bill.copyWith(
        status: status,
        sentAt: status == BillStatus.sent ? DateTime.now() : bill.sentAt,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateBill(updatedBill);

      final index = _bills.indexWhere((b) => b.id == billId);
      if (index != -1) {
        _bills[index] = updatedBill;
        _applyFilters();
      }

      return true;
    } catch (e) {
      debugPrint('Error updating bill status: $e');
      return false;
    }
  }

  Future<bool> sendBill(String billId, List<BillDeliveryMethod> methods) async {
    try {
      _isLoading.value = true;

      final bill = _bills.firstWhere((b) => b.id == billId);
      final updatedBill = bill.copyWith(
        status: BillStatus.sent,
        deliveryMethods: methods,
        sentAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateBill(updatedBill);

      final index = _bills.indexWhere((b) => b.id == billId);
      if (index != -1) {
        _bills[index] = updatedBill;
        _applyFilters();
      }

      // TODO: Implement actual sending logic (email, SMS, etc.)

      Get.snackbar(
        'Success',
        'Bill sent successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error sending bill: $e');
      Get.snackbar(
        'Error',
        'Failed to send bill',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Filtering and Searching
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  void setStatusFilter(String status) {
    _selectedStatus.value = status;
    _applyFilters();
  }

  void setCustomerFilter(String customerId) {
    _selectedCustomer.value = customerId;
    _applyFilters();
  }

  void setDateRange(DateRange? range) {
    _dateRange.value = range;
    _applyFilters();
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedStatus.value = 'all';
    _selectedCustomer.value = 'all';
    _dateRange.value = null;
    _applyFilters();
  }

  void _applyFilters() {
    List<Bill> filtered = List.from(_bills);

    // Search filter
    if (_searchQuery.value.isNotEmpty) {
      filtered =
          filtered.where((bill) {
            return bill.customerName.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                bill.id.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                );
          }).toList();
    }

    // Status filter
    if (_selectedStatus.value != 'all') {
      final status = BillStatus.values.firstWhere(
        (s) => s.name == _selectedStatus.value,
        orElse: () => BillStatus.generated,
      );
      filtered = filtered.where((bill) => bill.status == status).toList();
    }

    // Customer filter
    if (_selectedCustomer.value != 'all') {
      filtered =
          filtered
              .where((bill) => bill.customerId == _selectedCustomer.value)
              .toList();
    }

    // Date range filter
    if (_dateRange.value != null) {
      filtered =
          filtered.where((bill) {
            return _dateRange.value!.contains(bill.billDate);
          }).toList();
    }

    // Apply sorting
    _applySorting(filtered);
  }

  void setSorting(String sortField, bool ascending) {
    _sortBy.value = sortField;
    _sortAscending.value = ascending;
    _applyFilters();
  }

  void _applySorting(List<Bill> bills) {
    bills.sort((a, b) {
      int comparison = 0;

      switch (_sortBy.value) {
        case 'billDate':
          comparison = a.billDate.compareTo(b.billDate);
          break;
        case 'customerName':
          comparison = a.customerName.compareTo(b.customerName);
          break;
        case 'totalAmount':
          comparison = a.totalAmount.compareTo(b.totalAmount);
          break;
        case 'status':
          comparison = a.status.index.compareTo(b.status.index);
          break;
        case 'dueDate':
          final aDate = a.dueDate ?? DateTime(2099);
          final bDate = b.dueDate ?? DateTime(2099);
          comparison = aDate.compareTo(bDate);
          break;
        default:
          comparison = a.billDate.compareTo(b.billDate);
      }

      return _sortAscending.value ? comparison : -comparison;
    });

    _filteredBills.assignAll(bills);
  }

  // Quick access methods
  List<Bill> getTodaysBills() {
    final today = DateTime.now();
    return _bills.where((bill) {
      return bill.billDate.year == today.year &&
          bill.billDate.month == today.month &&
          bill.billDate.day == today.day;
    }).toList();
  }

  List<Bill> getPendingBills() {
    return _bills.where((bill) => bill.isPending).toList();
  }

  List<Bill> getOverdueBills() {
    return _bills.where((bill) => bill.isOverdue).toList();
  }

  List<Bill> getBillsByCustomer(String customerId) {
    return _bills.where((bill) => bill.customerId == customerId).toList();
  }

  double getTotalPendingAmount() {
    return _bills.fold(0.0, (sum, bill) => sum + bill.pendingAmount);
  }

  double getTotalRevenue() {
    return _bills.fold(0.0, (sum, bill) => sum + bill.totalAmount);
  }

  int getOverdueCount() {
    return _bills.where((bill) => bill.isOverdue).length;
  }
}
