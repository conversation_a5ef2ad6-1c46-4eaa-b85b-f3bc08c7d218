import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../../models/quality_control.dart';
import '../../models/user.dart';
import '../../models/milk_product.dart';
import '../../models/order.dart';
import '../../models/analytics.dart';
import '../../services/database_service.dart';
import '../../core/constants/app_colors.dart';

class QualityControlController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();

  // Observables
  final RxList<LeakageIncident> _incidents = <LeakageIncident>[].obs;
  final RxList<LeakageIncident> _filteredIncidents = <LeakageIncident>[].obs;
  final RxList<User> _suppliers = <User>[].obs;
  final RxList<User> _fieldStaff = <User>[].obs;
  final RxList<User> _customers = <User>[].obs;
  final RxList<MilkProduct> _products = <MilkProduct>[].obs;
  final RxList<Order> _orders = <Order>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedCause = 'all'.obs;
  final RxString _selectedResponsibleParty = 'all'.obs;
  final RxString _selectedStatus = 'all'.obs;
  final RxString _selectedSeverity = 'all'.obs;
  final RxString _selectedSupplier = 'all'.obs;
  final RxString _sortBy = 'incidentDate'.obs;
  final RxBool _sortAscending = false.obs;
  final Rx<DateRange?> _dateRange = Rx<DateRange?>(null);

  // Alert thresholds
  final RxDouble _criticalLossThreshold = 10000.0.obs; // Amount in rupees
  final RxInt _incidentFrequencyThreshold = 5.obs; // Incidents per week
  final RxDouble _defectRateThreshold = 2.0.obs; // Percentage

  // Getters
  List<LeakageIncident> get incidents => _incidents;
  List<LeakageIncident> get filteredIncidents => _filteredIncidents;
  List<User> get suppliers => _suppliers;
  List<User> get fieldStaff => _fieldStaff;
  List<User> get customers => _customers;
  List<MilkProduct> get products => _products;
  List<Order> get orders => _orders;
  bool get isLoading => _isLoading.value;
  String get searchQuery => _searchQuery.value;
  String get selectedCause => _selectedCause.value;
  String get selectedResponsibleParty => _selectedResponsibleParty.value;
  String get selectedStatus => _selectedStatus.value;
  String get selectedSeverity => _selectedSeverity.value;
  String get selectedSupplier => _selectedSupplier.value;
  String get sortBy => _sortBy.value;
  bool get sortAscending => _sortAscending.value;
  DateRange? get dateRange => _dateRange.value;
  double get criticalLossThreshold => _criticalLossThreshold.value;
  int get incidentFrequencyThreshold => _incidentFrequencyThreshold.value;
  double get defectRateThreshold => _defectRateThreshold.value;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    try {
      _isLoading.value = true;
      await Future.wait([
        loadIncidents(),
        loadSuppliers(),
        loadFieldStaff(),
        loadCustomers(),
        loadProducts(),
        loadOrders(),
      ]);
      _applyFilters();
    } catch (e) {
      debugPrint('Error loading initial data: $e');
      Get.snackbar(
        'Error',
        'Failed to load quality control data',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadIncidents() async {
    try {
      final incidentList = await _databaseService.getLeakageIncidents();
      _incidents.assignAll(incidentList);
    } catch (e) {
      debugPrint('Error loading incidents: $e');
      rethrow;
    }
  }

  Future<void> loadSuppliers() async {
    try {
      final users = await _databaseService.getUsers();
      // For now, use admin role for suppliers since supplier role doesn't exist
      _suppliers.assignAll(
        users
            .where(
              (user) =>
                  user.role == UserRole.admin &&
                  user.name.toLowerCase().contains('supplier'),
            )
            .toList(),
      );
    } catch (e) {
      debugPrint('Error loading suppliers: $e');
      rethrow;
    }
  }

  Future<void> loadFieldStaff() async {
    try {
      final users = await _databaseService.getUsers();
      _fieldStaff.assignAll(
        users.where((user) => user.role == UserRole.fieldMan).toList(),
      );
    } catch (e) {
      debugPrint('Error loading field staff: $e');
      rethrow;
    }
  }

  Future<void> loadCustomers() async {
    try {
      final users = await _databaseService.getUsers();
      _customers.assignAll(
        users.where((user) => user.role == UserRole.customer).toList(),
      );
    } catch (e) {
      debugPrint('Error loading customers: $e');
      rethrow;
    }
  }

  Future<void> loadProducts() async {
    try {
      final productList = await _databaseService.getMilkProducts();
      _products.assignAll(productList);
    } catch (e) {
      debugPrint('Error loading products: $e');
      rethrow;
    }
  }

  Future<void> loadOrders() async {
    try {
      final orderList = await _databaseService.getOrders();
      _orders.assignAll(orderList);
    } catch (e) {
      debugPrint('Error loading orders: $e');
      rethrow;
    }
  }

  // CRUD Operations for Leakage Incidents
  Future<bool> createIncident({
    required DateTime incidentDate,
    required String productId,
    String? batchNumber,
    DateTime? expiryDate,
    required double quantityAffected,
    required int packetsAffected,
    required double unitCost,
    required LeakageCause cause,
    required ResponsibleParty responsibleParty,
    required LeakageSeverity severity,
    required String description,
    List<String>? photoUrls,
    String? supplierId,
    String? fieldManId,
    String? customerId,
    String? orderId,
    String? preventiveActions,
  }) async {
    try {
      _isLoading.value = true;

      final product = _products.firstWhere((p) => p.id == productId);
      final supplier =
          supplierId != null
              ? _suppliers.firstWhereOrNull((s) => s.id == supplierId)
              : null;
      final fieldMan =
          fieldManId != null
              ? _fieldStaff.firstWhereOrNull((f) => f.id == fieldManId)
              : null;
      final customer =
          customerId != null
              ? _customers.firstWhereOrNull((c) => c.id == customerId)
              : null;

      final totalLoss = quantityAffected * unitCost;

      final incident = LeakageIncident(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        incidentDate: incidentDate,
        productId: productId,
        productName: product.name,
        productCategory: product.category.name,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        quantityAffected: quantityAffected,
        packetsAffected: packetsAffected,
        unitCost: unitCost,
        totalLoss: totalLoss,
        cause: cause,
        responsibleParty: responsibleParty,
        status: LeakageStatus.reported,
        severity: severity,
        description: description,
        photoUrls: photoUrls ?? [],
        supplierId: supplierId,
        supplierName: supplier?.name,
        fieldManId: fieldManId,
        fieldManName: fieldMan?.name,
        customerId: customerId,
        customerName: customer?.name,
        orderId: orderId,
        reportedBy: 'admin', // TODO: Get current user
        reportedAt: DateTime.now(),
        preventiveActions: preventiveActions,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.insertLeakageIncident(incident);
      _incidents.insert(0, incident);
      _applyFilters();

      // Check for critical alerts
      _checkCriticalAlerts(incident);

      Get.snackbar(
        'Success',
        'Leakage incident recorded successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error creating incident: $e');
      Get.snackbar(
        'Error',
        'Failed to record leakage incident',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateIncident(LeakageIncident incident) async {
    try {
      _isLoading.value = true;

      final updatedIncident = incident.copyWith(updatedAt: DateTime.now());

      await _databaseService.updateLeakageIncident(updatedIncident);

      final index = _incidents.indexWhere((i) => i.id == incident.id);
      if (index != -1) {
        _incidents[index] = updatedIncident;
        _applyFilters();
      }

      Get.snackbar(
        'Success',
        'Incident updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error updating incident: $e');
      Get.snackbar(
        'Error',
        'Failed to update incident',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deleteIncident(String incidentId) async {
    try {
      _isLoading.value = true;

      await _databaseService.deleteLeakageIncident(incidentId);
      _incidents.removeWhere((incident) => incident.id == incidentId);
      _applyFilters();

      Get.snackbar(
        'Success',
        'Incident deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error deleting incident: $e');
      Get.snackbar(
        'Error',
        'Failed to delete incident',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Status Management
  Future<bool> updateIncidentStatus(
    String incidentId,
    LeakageStatus status, {
    String? investigatedBy,
    String? resolutionNotes,
    double? compensationAmount,
    String? preventiveActions,
  }) async {
    try {
      final incident = _incidents.firstWhere((i) => i.id == incidentId);
      final updatedIncident = incident.copyWith(
        status: status,
        investigatedBy:
            status == LeakageStatus.investigating
                ? (investigatedBy ?? 'admin')
                : incident.investigatedBy,
        investigatedAt:
            status == LeakageStatus.investigating
                ? DateTime.now()
                : incident.investigatedAt,
        resolutionNotes: resolutionNotes ?? incident.resolutionNotes,
        resolvedAt:
            status == LeakageStatus.resolved || status == LeakageStatus.closed
                ? DateTime.now()
                : incident.resolvedAt,
        compensationAmount: compensationAmount ?? incident.compensationAmount,
        isCompensated: compensationAmount != null,
        preventiveActions: preventiveActions ?? incident.preventiveActions,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateLeakageIncident(updatedIncident);

      final index = _incidents.indexWhere((i) => i.id == incidentId);
      if (index != -1) {
        _incidents[index] = updatedIncident;
        _applyFilters();
      }

      return true;
    } catch (e) {
      debugPrint('Error updating incident status: $e');
      return false;
    }
  }

  // Critical Alerts
  void _checkCriticalAlerts(LeakageIncident incident) {
    // High value loss alert
    if (incident.totalLoss >= _criticalLossThreshold.value) {
      Get.snackbar(
        'Critical Alert',
        'High value loss detected: ₹${incident.totalLoss.toStringAsFixed(2)}',
        snackPosition: SnackPosition.TOP,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
        duration: const Duration(seconds: 5),
      );
    }

    // Critical severity alert
    if (incident.severity == LeakageSeverity.critical) {
      Get.snackbar(
        'Critical Severity',
        'Critical leakage incident reported for ${incident.productName}',
        snackPosition: SnackPosition.TOP,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
        duration: const Duration(seconds: 5),
      );
    }

    // Supplier pattern alert
    if (incident.supplierId != null) {
      final supplierIncidents =
          _incidents
              .where(
                (i) =>
                    i.supplierId == incident.supplierId &&
                    i.incidentDate.isAfter(
                      DateTime.now().subtract(const Duration(days: 7)),
                    ),
              )
              .length;

      if (supplierIncidents >= _incidentFrequencyThreshold.value) {
        Get.snackbar(
          'Supplier Alert',
          'Multiple incidents from ${incident.supplierName} this week',
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.warning,
          colorText: AppColors.textOnPrimary,
          duration: const Duration(seconds: 5),
        );
      }
    }
  }

  // Filtering and Searching
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  void setCauseFilter(String cause) {
    _selectedCause.value = cause;
    _applyFilters();
  }

  void setResponsiblePartyFilter(String party) {
    _selectedResponsibleParty.value = party;
    _applyFilters();
  }

  void setStatusFilter(String status) {
    _selectedStatus.value = status;
    _applyFilters();
  }

  void setSeverityFilter(String severity) {
    _selectedSeverity.value = severity;
    _applyFilters();
  }

  void setSupplierFilter(String supplierId) {
    _selectedSupplier.value = supplierId;
    _applyFilters();
  }

  void setDateRange(DateRange? range) {
    _dateRange.value = range;
    _applyFilters();
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedCause.value = 'all';
    _selectedResponsibleParty.value = 'all';
    _selectedStatus.value = 'all';
    _selectedSeverity.value = 'all';
    _selectedSupplier.value = 'all';
    _dateRange.value = null;
    _applyFilters();
  }

  void _applyFilters() {
    List<LeakageIncident> filtered = List.from(_incidents);

    // Search filter
    if (_searchQuery.value.isNotEmpty) {
      filtered =
          filtered.where((incident) {
            return incident.productName.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                incident.description.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                incident.batchNumber?.toLowerCase().contains(
                      _searchQuery.value.toLowerCase(),
                    ) ==
                    true;
          }).toList();
    }

    // Cause filter
    if (_selectedCause.value != 'all') {
      final cause = LeakageCause.values.firstWhere(
        (c) => c.name == _selectedCause.value,
        orElse: () => LeakageCause.unknown,
      );
      filtered = filtered.where((incident) => incident.cause == cause).toList();
    }

    // Responsible party filter
    if (_selectedResponsibleParty.value != 'all') {
      final party = ResponsibleParty.values.firstWhere(
        (p) => p.name == _selectedResponsibleParty.value,
        orElse: () => ResponsibleParty.unknown,
      );
      filtered =
          filtered
              .where((incident) => incident.responsibleParty == party)
              .toList();
    }

    // Status filter
    if (_selectedStatus.value != 'all') {
      final status = LeakageStatus.values.firstWhere(
        (s) => s.name == _selectedStatus.value,
        orElse: () => LeakageStatus.reported,
      );
      filtered =
          filtered.where((incident) => incident.status == status).toList();
    }

    // Severity filter
    if (_selectedSeverity.value != 'all') {
      final severity = LeakageSeverity.values.firstWhere(
        (s) => s.name == _selectedSeverity.value,
        orElse: () => LeakageSeverity.minor,
      );
      filtered =
          filtered.where((incident) => incident.severity == severity).toList();
    }

    // Supplier filter
    if (_selectedSupplier.value != 'all') {
      filtered =
          filtered
              .where(
                (incident) => incident.supplierId == _selectedSupplier.value,
              )
              .toList();
    }

    // Date range filter
    if (_dateRange.value != null) {
      filtered =
          filtered.where((incident) {
            return _dateRange.value!.contains(incident.incidentDate);
          }).toList();
    }

    // Apply sorting
    _applySorting(filtered);
  }

  void setSorting(String sortField, bool ascending) {
    _sortBy.value = sortField;
    _sortAscending.value = ascending;
    _applyFilters();
  }

  void _applySorting(List<LeakageIncident> incidents) {
    incidents.sort((a, b) {
      int comparison = 0;

      switch (_sortBy.value) {
        case 'incidentDate':
          comparison = a.incidentDate.compareTo(b.incidentDate);
          break;
        case 'productName':
          comparison = a.productName.compareTo(b.productName);
          break;
        case 'totalLoss':
          comparison = a.totalLoss.compareTo(b.totalLoss);
          break;
        case 'severity':
          comparison = a.severity.index.compareTo(b.severity.index);
          break;
        case 'status':
          comparison = a.status.index.compareTo(b.status.index);
          break;
        default:
          comparison = a.incidentDate.compareTo(b.incidentDate);
      }

      return _sortAscending.value ? comparison : -comparison;
    });

    _filteredIncidents.assignAll(incidents);
  }

  // Analytics and Reporting
  Map<String, dynamic> getQualityAnalytics({DateRange? range}) {
    final analysisRange = range ?? DateRange.thisMonth();
    final incidentsInRange =
        _incidents
            .where((incident) => analysisRange.contains(incident.incidentDate))
            .toList();

    final totalIncidents = incidentsInRange.length;
    final totalLoss = incidentsInRange.fold(
      0.0,
      (sum, incident) => sum + incident.totalLoss,
    );
    final totalPacketsAffected = incidentsInRange.fold(
      0,
      (sum, incident) => sum + incident.packetsAffected,
    );
    final totalQuantityAffected = incidentsInRange.fold(
      0.0,
      (sum, incident) => sum + incident.quantityAffected,
    );

    // Cause analysis
    final Map<String, int> causeAnalysis = {};
    for (final cause in LeakageCause.values) {
      causeAnalysis[cause.name] =
          incidentsInRange.where((i) => i.cause == cause).length;
    }

    // Severity analysis
    final Map<String, int> severityAnalysis = {};
    for (final severity in LeakageSeverity.values) {
      severityAnalysis[severity.name] =
          incidentsInRange.where((i) => i.severity == severity).length;
    }

    // Responsible party analysis
    final Map<String, int> responsiblePartyAnalysis = {};
    for (final party in ResponsibleParty.values) {
      responsiblePartyAnalysis[party.name] =
          incidentsInRange.where((i) => i.responsibleParty == party).length;
    }

    // Supplier performance
    final Map<String, Map<String, dynamic>> supplierPerformance = {};
    for (final incident in incidentsInRange.where(
      (i) => i.supplierId != null,
    )) {
      final supplierId = incident.supplierId!;
      if (!supplierPerformance.containsKey(supplierId)) {
        supplierPerformance[supplierId] = {
          'name': incident.supplierName,
          'incidents': 0,
          'totalLoss': 0.0,
          'packetsAffected': 0,
        };
      }
      supplierPerformance[supplierId]!['incidents'] =
          (supplierPerformance[supplierId]!['incidents'] as int) + 1;
      supplierPerformance[supplierId]!['totalLoss'] =
          (supplierPerformance[supplierId]!['totalLoss'] as double) +
          incident.totalLoss;
      supplierPerformance[supplierId]!['packetsAffected'] =
          (supplierPerformance[supplierId]!['packetsAffected'] as int) +
          incident.packetsAffected;
    }

    return {
      'totalIncidents': totalIncidents,
      'totalLoss': totalLoss,
      'totalPacketsAffected': totalPacketsAffected,
      'totalQuantityAffected': totalQuantityAffected,
      'averageLossPerIncident':
          totalIncidents > 0 ? totalLoss / totalIncidents : 0,
      'causeAnalysis': causeAnalysis,
      'severityAnalysis': severityAnalysis,
      'responsiblePartyAnalysis': responsiblePartyAnalysis,
      'supplierPerformance': supplierPerformance,
      'dateRange': analysisRange,
    };
  }

  // Supplier Quality Scorecard
  SupplierQualityScorecard generateSupplierScorecard(
    String supplierId, {
    DateRange? range,
  }) {
    final analysisRange = range ?? DateRange.thisMonth();
    final supplierIncidents =
        _incidents
            .where(
              (incident) =>
                  incident.supplierId == supplierId &&
                  analysisRange.contains(incident.incidentDate),
            )
            .toList();

    final supplier = _suppliers.firstWhere((s) => s.id == supplierId);
    final totalIncidents = supplierIncidents.length;
    final totalLossAmount = supplierIncidents.fold(
      0.0,
      (sum, incident) => sum + incident.totalLoss,
    );

    // Calculate total packets supplied (this would need to be tracked separately in a real system)
    final totalPacketsSupplied = 10000; // Placeholder value
    final defectRate =
        totalPacketsSupplied > 0
            ? (supplierIncidents.fold(
                      0,
                      (sum, incident) => sum + incident.packetsAffected,
                    ) /
                    totalPacketsSupplied) *
                100
            : 0.0;

    // Incidents by cause
    final Map<LeakageCause, int> incidentsByCause = {};
    for (final cause in LeakageCause.values) {
      incidentsByCause[cause] =
          supplierIncidents.where((i) => i.cause == cause).length;
    }

    // Incidents by severity
    final Map<LeakageSeverity, int> incidentsBySeverity = {};
    for (final severity in LeakageSeverity.values) {
      incidentsBySeverity[severity] =
          supplierIncidents.where((i) => i.severity == severity).length;
    }

    // Average resolution time
    final resolvedIncidents = supplierIncidents.where(
      (i) => i.isResolved && i.resolvedAt != null,
    );
    final averageResolutionTime =
        resolvedIncidents.isNotEmpty
            ? resolvedIncidents
                    .map((i) => i.resolvedAt!.difference(i.reportedAt).inDays)
                    .reduce((a, b) => a + b) /
                resolvedIncidents.length
            : 0.0;

    // Compensation details
    final compensatedIncidents =
        supplierIncidents.where((i) => i.isCompensated).length;
    final compensationAmount = supplierIncidents.fold(
      0.0,
      (sum, incident) => sum + (incident.compensationAmount ?? 0.0),
    );

    // Quality score calculation (0-100)
    double qualityScore = 100.0;
    qualityScore -= (defectRate * 10); // Reduce score based on defect rate
    qualityScore -= (totalIncidents * 2); // Reduce score for each incident
    qualityScore -=
        (totalLossAmount / 1000); // Reduce score based on loss amount
    qualityScore = qualityScore.clamp(0.0, 100.0);

    return SupplierQualityScorecard(
      supplierId: supplierId,
      supplierName: supplier.name,
      totalIncidents: totalIncidents,
      totalLossAmount: totalLossAmount,
      totalPacketsSupplied: totalPacketsSupplied,
      defectRate: defectRate,
      incidentsByCause: incidentsByCause,
      incidentsBySeverity: incidentsBySeverity,
      averageResolutionTime: averageResolutionTime,
      compensatedIncidents: compensatedIncidents,
      compensationAmount: compensationAmount,
      fromDate: analysisRange.startDate,
      toDate: analysisRange.endDate,
      qualityScore: qualityScore,
    );
  }

  // Quick access methods
  List<LeakageIncident> getTodaysIncidents() {
    final today = DateTime.now();
    return _incidents.where((incident) {
      return incident.incidentDate.year == today.year &&
          incident.incidentDate.month == today.month &&
          incident.incidentDate.day == today.day;
    }).toList();
  }

  List<LeakageIncident> getPendingIncidents() {
    return _incidents.where((incident) => incident.isPending).toList();
  }

  List<LeakageIncident> getCriticalIncidents() {
    return _incidents
        .where(
          (incident) =>
              incident.severity == LeakageSeverity.critical ||
              incident.totalLoss >= _criticalLossThreshold.value,
        )
        .toList();
  }

  double getTotalLossAmount() {
    return _incidents.fold(0.0, (sum, incident) => sum + incident.totalLoss);
  }

  int getCriticalIncidentsCount() {
    return getCriticalIncidents().length;
  }

  // Settings
  void updateCriticalLossThreshold(double threshold) {
    _criticalLossThreshold.value = threshold;
  }

  void updateIncidentFrequencyThreshold(int threshold) {
    _incidentFrequencyThreshold.value = threshold;
  }

  void updateDefectRateThreshold(double threshold) {
    _defectRateThreshold.value = threshold;
  }
}
