import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/user.dart';
import '../../services/database_service.dart';
import '../../core/constants/app_constants.dart';

class AuthController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();
  
  // Observables
  final Rx<User?> _currentUser = Rx<User?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isLoggedIn = false.obs;
  
  // Getters
  User? get currentUser => _currentUser.value;
  bool get isLoading => _isLoading.value;
  bool get isLoggedIn => _isLoggedIn.value;
  
  @override
  void onInit() {
    super.onInit();
    checkAuthStatus();
  }
  
  Future<void> checkAuthStatus() async {
    try {
      _isLoading.value = true;
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      
      if (userId != null) {
        final user = await _databaseService.getUserById(userId);
        if (user != null && user.isActive) {
          _currentUser.value = user;
          _isLoggedIn.value = true;
          _navigateToRoleDashboard(user.role);
        } else {
          _logout();
        }
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to check authentication status',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<bool> login(String email, String password) async {
    try {
      _isLoading.value = true;
      
      // Validate credentials
      if (!AppConstants.demoCredentials.containsKey(email) ||
          AppConstants.demoCredentials[email] != password) {
        Get.snackbar(
          'Login Failed',
          'Invalid email or password',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }
      
      // Get user from database
      final users = await _databaseService.getUsers();
      final user = users.firstWhere(
        (u) => u.email.toLowerCase() == email.toLowerCase(),
        orElse: () => throw Exception('User not found'),
      );
      
      if (!user.isActive) {
        Get.snackbar(
          'Login Failed',
          'Account is inactive',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }
      
      // Save user session
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_id', user.id);
      
      _currentUser.value = user;
      _isLoggedIn.value = true;
      
      Get.snackbar(
        'Welcome!',
        'Login successful',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      _navigateToRoleDashboard(user.role);
      return true;
      
    } catch (e) {
      Get.snackbar(
        'Login Failed',
        'An error occurred during login',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<bool> register({
    required String name,
    required String email,
    required String phone,
    required UserRole role,
    String? address,
  }) async {
    try {
      _isLoading.value = true;
      
      final user = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        email: email,
        phone: phone,
        role: role,
        address: address,
        createdAt: DateTime.now(),
      );
      
      await _databaseService.insertUser(user);
      
      Get.snackbar(
        'Success',
        'Account created successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    } catch (e) {
      Get.snackbar(
        'Registration Failed',
        'Failed to create account',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<void> logout() async {
    await _logout();
    Get.offAllNamed('/login');
  }
  
  Future<void> _logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_id');
    _currentUser.value = null;
    _isLoggedIn.value = false;
  }
  
  void _navigateToRoleDashboard(UserRole role) {
    switch (role) {
      case UserRole.admin:
        Get.offAllNamed('/admin');
        break;
      case UserRole.customer:
        Get.offAllNamed('/customer');
        break;
      case UserRole.fieldMan:
        Get.offAllNamed('/field-man');
        break;
    }
  }
  
  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? address,
  }) async {
    if (_currentUser.value == null) return false;
    
    try {
      _isLoading.value = true;
      
      final updatedUser = _currentUser.value!.copyWith(
        name: name ?? _currentUser.value!.name,
        phone: phone ?? _currentUser.value!.phone,
        address: address ?? _currentUser.value!.address,
      );
      
      _currentUser.value = updatedUser;
      
      Get.snackbar(
        'Success',
        'Profile updated successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update profile',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  // Role checking methods
  bool hasRole(UserRole role) => _currentUser.value?.role == role;
  bool isAdmin() => hasRole(UserRole.admin);
  bool isCustomer() => hasRole(UserRole.customer);
  bool isFieldMan() => hasRole(UserRole.fieldMan);
}
