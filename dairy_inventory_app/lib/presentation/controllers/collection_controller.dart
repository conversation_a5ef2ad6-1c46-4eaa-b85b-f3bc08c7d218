import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../../models/payment_collection.dart';
import '../../models/billing.dart';
import '../../models/user.dart';
import '../../models/analytics.dart';
import '../../services/database_service.dart';
import '../../core/constants/app_colors.dart';

class CollectionController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();

  // Observables
  final RxList<PaymentCollection> _collections = <PaymentCollection>[].obs;
  final RxList<PaymentCollection> _filteredCollections =
      <PaymentCollection>[].obs;
  final RxList<User> _fieldStaff = <User>[].obs;
  final RxList<User> _customers = <User>[].obs;
  final RxList<Bill> _bills = <Bill>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedPaymentMethod = 'all'.obs;
  final RxString _selectedFieldStaff = 'all'.obs;
  final RxString _selectedStatus = 'all'.obs;
  final RxString _sortBy = 'collectionDate'.obs;
  final RxBool _sortAscending = false.obs;
  final Rx<DateRange?> _dateRange = Rx<DateRange?>(null);

  // Collection targets
  final RxDouble _dailyTarget = 50000.0.obs;
  final RxDouble _weeklyTarget = 350000.0.obs;
  final RxDouble _monthlyTarget = 1500000.0.obs;

  // Getters
  List<PaymentCollection> get collections => _collections;
  List<PaymentCollection> get filteredCollections => _filteredCollections;
  List<User> get fieldStaff => _fieldStaff;
  List<User> get customers => _customers;
  List<Bill> get bills => _bills;
  bool get isLoading => _isLoading.value;
  String get searchQuery => _searchQuery.value;
  String get selectedPaymentMethod => _selectedPaymentMethod.value;
  String get selectedFieldStaff => _selectedFieldStaff.value;
  String get selectedStatus => _selectedStatus.value;
  String get sortBy => _sortBy.value;
  bool get sortAscending => _sortAscending.value;
  DateRange? get dateRange => _dateRange.value;
  double get dailyTarget => _dailyTarget.value;
  double get weeklyTarget => _weeklyTarget.value;
  double get monthlyTarget => _monthlyTarget.value;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    try {
      _isLoading.value = true;
      await Future.wait([
        loadCollections(),
        loadFieldStaff(),
        loadCustomers(),
        loadBills(),
      ]);
      _applyFilters();
    } catch (e) {
      debugPrint('Error loading initial data: $e');
      Get.snackbar(
        'Error',
        'Failed to load collection data',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadCollections() async {
    try {
      final collectionList = await _databaseService.getPaymentCollections();
      _collections.assignAll(collectionList);
    } catch (e) {
      debugPrint('Error loading collections: $e');
      rethrow;
    }
  }

  Future<void> loadFieldStaff() async {
    try {
      final users = await _databaseService.getUsers();
      _fieldStaff.assignAll(
        users.where((user) => user.role == UserRole.fieldMan).toList(),
      );
    } catch (e) {
      debugPrint('Error loading field staff: $e');
      rethrow;
    }
  }

  Future<void> loadCustomers() async {
    try {
      final users = await _databaseService.getUsers();
      _customers.assignAll(
        users.where((user) => user.role == UserRole.customer).toList(),
      );
    } catch (e) {
      debugPrint('Error loading customers: $e');
      rethrow;
    }
  }

  Future<void> loadBills() async {
    try {
      final billList = await _databaseService.getBills();
      _bills.assignAll(billList);
    } catch (e) {
      debugPrint('Error loading bills: $e');
      rethrow;
    }
  }

  // Collection Recording
  Future<bool> recordPayment({
    required String customerId,
    String? billId,
    required double amount,
    required PaymentMethod paymentMethod,
    required DateTime collectionDate,
    String? fieldManId,
    String? referenceNumber,
    String? chequeNumber,
    String? bankName,
    DateTime? chequeDate,
    String? notes,
    List<String>? attachments,
  }) async {
    try {
      _isLoading.value = true;

      final customer = _customers.firstWhere((c) => c.id == customerId);
      final fieldMan =
          fieldManId != null
              ? _fieldStaff.firstWhereOrNull((f) => f.id == fieldManId)
              : null;

      final collection = PaymentCollection(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        customerId: customerId,
        customerName: customer.name,
        billId: billId,
        amount: amount,
        paymentMethod: paymentMethod,
        status:
            paymentMethod == PaymentMethod.cheque
                ? PaymentStatus.pending
                : PaymentStatus.completed,
        collectionDate: collectionDate,
        collectedBy: 'admin', // TODO: Get current user
        fieldManId: fieldManId,
        fieldManName: fieldMan?.name,
        referenceNumber: referenceNumber,
        chequeNumber: chequeNumber,
        bankName: bankName,
        chequeDate: chequeDate,
        chequeStatus:
            paymentMethod == PaymentMethod.cheque ? ChequeStatus.pending : null,
        notes: notes,
        attachments: attachments ?? [],
        isVerified: paymentMethod != PaymentMethod.cheque,
        verifiedBy: paymentMethod != PaymentMethod.cheque ? 'admin' : null,
        verifiedAt:
            paymentMethod != PaymentMethod.cheque ? DateTime.now() : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.insertPaymentCollection(collection);
      _collections.insert(0, collection);
      _applyFilters();

      // Update bill if specified
      if (billId != null) {
        await _updateBillPayment(billId, amount);
      }

      Get.snackbar(
        'Success',
        'Payment recorded successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error recording payment: $e');
      Get.snackbar(
        'Error',
        'Failed to record payment',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _updateBillPayment(String billId, double amount) async {
    try {
      final bill = _bills.firstWhere((b) => b.id == billId);
      final updatedBill = bill.copyWith(
        paidAmount: bill.paidAmount + amount,
        pendingAmount: bill.pendingAmount - amount,
        status:
            (bill.pendingAmount - amount) <= 0 ? BillStatus.paid : bill.status,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateBill(updatedBill);

      final index = _bills.indexWhere((b) => b.id == billId);
      if (index != -1) {
        _bills[index] = updatedBill;
      }
    } catch (e) {
      debugPrint('Error updating bill payment: $e');
    }
  }

  // Cheque Management
  Future<bool> updateChequeStatus(
    String collectionId,
    ChequeStatus status, {
    DateTime? clearanceDate,
  }) async {
    try {
      final collection = _collections.firstWhere((c) => c.id == collectionId);
      final updatedCollection = collection.copyWith(
        chequeStatus: status,
        clearanceDate: clearanceDate,
        status:
            status == ChequeStatus.cleared
                ? PaymentStatus.completed
                : status == ChequeStatus.bounced
                ? PaymentStatus.failed
                : PaymentStatus.pending,
        isVerified: status == ChequeStatus.cleared,
        verifiedBy:
            status == ChequeStatus.cleared ? 'admin' : collection.verifiedBy,
        verifiedAt:
            status == ChequeStatus.cleared
                ? DateTime.now()
                : collection.verifiedAt,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updatePaymentCollection(updatedCollection);

      final index = _collections.indexWhere((c) => c.id == collectionId);
      if (index != -1) {
        _collections[index] = updatedCollection;
        _applyFilters();
      }

      // If cheque bounced, reverse the bill payment
      if (status == ChequeStatus.bounced && collection.billId != null) {
        await _reverseBillPayment(collection.billId!, collection.amount);
      }

      Get.snackbar(
        'Success',
        'Cheque status updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error updating cheque status: $e');
      Get.snackbar(
        'Error',
        'Failed to update cheque status',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    }
  }

  Future<void> _reverseBillPayment(String billId, double amount) async {
    try {
      final bill = _bills.firstWhere((b) => b.id == billId);
      final updatedBill = bill.copyWith(
        paidAmount: bill.paidAmount - amount,
        pendingAmount: bill.pendingAmount + amount,
        status: BillStatus.overdue,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateBill(updatedBill);

      final index = _bills.indexWhere((b) => b.id == billId);
      if (index != -1) {
        _bills[index] = updatedBill;
      }
    } catch (e) {
      debugPrint('Error reversing bill payment: $e');
    }
  }

  // Verification
  Future<bool> verifyPayment(String collectionId) async {
    try {
      final collection = _collections.firstWhere((c) => c.id == collectionId);
      final updatedCollection = collection.copyWith(
        isVerified: true,
        verifiedBy: 'admin', // TODO: Get current user
        verifiedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.updatePaymentCollection(updatedCollection);

      final index = _collections.indexWhere((c) => c.id == collectionId);
      if (index != -1) {
        _collections[index] = updatedCollection;
        _applyFilters();
      }

      return true;
    } catch (e) {
      debugPrint('Error verifying payment: $e');
      return false;
    }
  }

  // Filtering and Searching
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  void setPaymentMethodFilter(String method) {
    _selectedPaymentMethod.value = method;
    _applyFilters();
  }

  void setFieldStaffFilter(String fieldStaffId) {
    _selectedFieldStaff.value = fieldStaffId;
    _applyFilters();
  }

  void setStatusFilter(String status) {
    _selectedStatus.value = status;
    _applyFilters();
  }

  void setDateRange(DateRange? range) {
    _dateRange.value = range;
    _applyFilters();
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedPaymentMethod.value = 'all';
    _selectedFieldStaff.value = 'all';
    _selectedStatus.value = 'all';
    _dateRange.value = null;
    _applyFilters();
  }

  void _applyFilters() {
    List<PaymentCollection> filtered = List.from(_collections);

    // Search filter
    if (_searchQuery.value.isNotEmpty) {
      filtered =
          filtered.where((collection) {
            return collection.customerName.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                collection.id.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                collection.referenceNumber?.toLowerCase().contains(
                      _searchQuery.value.toLowerCase(),
                    ) ==
                    true;
          }).toList();
    }

    // Payment method filter
    if (_selectedPaymentMethod.value != 'all') {
      final method = PaymentMethod.values.firstWhere(
        (m) => m.name == _selectedPaymentMethod.value,
        orElse: () => PaymentMethod.cash,
      );
      filtered =
          filtered
              .where((collection) => collection.paymentMethod == method)
              .toList();
    }

    // Field staff filter
    if (_selectedFieldStaff.value != 'all') {
      filtered =
          filtered
              .where(
                (collection) =>
                    collection.fieldManId == _selectedFieldStaff.value,
              )
              .toList();
    }

    // Status filter
    if (_selectedStatus.value != 'all') {
      final status = PaymentStatus.values.firstWhere(
        (s) => s.name == _selectedStatus.value,
        orElse: () => PaymentStatus.completed,
      );
      filtered =
          filtered.where((collection) => collection.status == status).toList();
    }

    // Date range filter
    if (_dateRange.value != null) {
      filtered =
          filtered.where((collection) {
            return _dateRange.value!.contains(collection.collectionDate);
          }).toList();
    }

    // Apply sorting
    _applySorting(filtered);
  }

  void setSorting(String sortField, bool ascending) {
    _sortBy.value = sortField;
    _sortAscending.value = ascending;
    _applyFilters();
  }

  void _applySorting(List<PaymentCollection> collections) {
    collections.sort((a, b) {
      int comparison = 0;

      switch (_sortBy.value) {
        case 'collectionDate':
          comparison = a.collectionDate.compareTo(b.collectionDate);
          break;
        case 'customerName':
          comparison = a.customerName.compareTo(b.customerName);
          break;
        case 'amount':
          comparison = a.amount.compareTo(b.amount);
          break;
        case 'paymentMethod':
          comparison = a.paymentMethod.index.compareTo(b.paymentMethod.index);
          break;
        case 'status':
          comparison = a.status.index.compareTo(b.status.index);
          break;
        default:
          comparison = a.collectionDate.compareTo(b.collectionDate);
      }

      return _sortAscending.value ? comparison : -comparison;
    });

    _filteredCollections.assignAll(collections);
  }

  // Quick access methods
  List<PaymentCollection> getTodaysCollections() {
    final today = DateTime.now();
    return _collections.where((collection) {
      return collection.collectionDate.year == today.year &&
          collection.collectionDate.month == today.month &&
          collection.collectionDate.day == today.day;
    }).toList();
  }

  List<PaymentCollection> getPendingVerifications() {
    return _collections
        .where(
          (collection) =>
              collection.requiresVerification && !collection.isVerified,
        )
        .toList();
  }

  double getTodaysCollection() {
    return getTodaysCollections()
        .where((c) => c.status == PaymentStatus.completed)
        .fold(0.0, (sum, c) => sum + c.amount);
  }

  double getTargetAchievement() {
    final todaysCollection = getTodaysCollection();
    return _dailyTarget.value > 0
        ? (todaysCollection / _dailyTarget.value) * 100
        : 0;
  }

  // Target management
  void updateDailyTarget(double target) {
    _dailyTarget.value = target;
  }

  void updateWeeklyTarget(double target) {
    _weeklyTarget.value = target;
  }

  void updateMonthlyTarget(double target) {
    _monthlyTarget.value = target;
  }
}
