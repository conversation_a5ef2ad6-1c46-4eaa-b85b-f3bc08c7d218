import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../../models/order.dart';
import '../../models/user.dart';
import '../../models/analytics.dart';
import '../../services/database_service.dart';
import '../../core/constants/app_colors.dart';

class AdminOrderController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();

  // Observables
  final RxList<Order> _orders = <Order>[].obs;
  final RxList<Order> _filteredOrders = <Order>[].obs;
  final RxList<User> _fieldStaff = <User>[].obs;
  final RxList<User> _customers = <User>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedStatus = 'all'.obs;
  final RxString _selectedPriority = 'all'.obs;
  final RxString _selectedCategory = 'all'.obs;
  final RxString _selectedFieldMan = 'all'.obs;
  final RxString _sortBy = 'orderDate'.obs;
  final RxBool _sortAscending = false.obs;
  final Rx<DateRange?> _dateRange = Rx<DateRange?>(null);

  // Getters
  List<Order> get orders => _orders;
  List<Order> get filteredOrders => _filteredOrders;
  List<User> get fieldStaff => _fieldStaff;
  List<User> get customers => _customers;
  bool get isLoading => _isLoading.value;
  String get searchQuery => _searchQuery.value;
  String get selectedStatus => _selectedStatus.value;
  String get selectedPriority => _selectedPriority.value;
  String get selectedCategory => _selectedCategory.value;
  String get selectedFieldMan => _selectedFieldMan.value;
  String get sortBy => _sortBy.value;
  bool get sortAscending => _sortAscending.value;
  DateRange? get dateRange => _dateRange.value;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    try {
      _isLoading.value = true;
      await Future.wait([loadOrders(), loadFieldStaff(), loadCustomers()]);
      _applyFilters();
    } catch (e) {
      debugPrint('Error loading initial data: $e');
      Get.snackbar(
        'Error',
        'Failed to load data',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadOrders() async {
    try {
      final orderList = await _databaseService.getOrders();
      _orders.assignAll(orderList);
    } catch (e) {
      debugPrint('Error loading orders: $e');
      rethrow;
    }
  }

  Future<void> loadFieldStaff() async {
    try {
      final users = await _databaseService.getUsers();
      _fieldStaff.assignAll(
        users.where((user) => user.role == UserRole.fieldMan).toList(),
      );
    } catch (e) {
      debugPrint('Error loading field staff: $e');
      rethrow;
    }
  }

  Future<void> loadCustomers() async {
    try {
      final users = await _databaseService.getUsers();
      _customers.assignAll(
        users.where((user) => user.role == UserRole.customer).toList(),
      );
    } catch (e) {
      debugPrint('Error loading customers: $e');
      rethrow;
    }
  }

  // CRUD Operations
  Future<bool> createOrder(Order order) async {
    try {
      _isLoading.value = true;
      await _databaseService.insertOrder(order);
      _orders.insert(0, order);
      _applyFilters();

      Get.snackbar(
        'Success',
        'Order created successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );
      return true;
    } catch (e) {
      debugPrint('Error creating order: $e');
      Get.snackbar(
        'Error',
        'Failed to create order',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateOrder(Order order) async {
    try {
      _isLoading.value = true;
      await _databaseService.updateOrder(order);

      final index = _orders.indexWhere((o) => o.id == order.id);
      if (index != -1) {
        _orders[index] = order;
        _applyFilters();
      }

      Get.snackbar(
        'Success',
        'Order updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );
      return true;
    } catch (e) {
      debugPrint('Error updating order: $e');
      Get.snackbar(
        'Error',
        'Failed to update order',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deleteOrder(String orderId) async {
    try {
      _isLoading.value = true;
      await _databaseService.deleteOrder(orderId);
      _orders.removeWhere((order) => order.id == orderId);
      _applyFilters();

      Get.snackbar(
        'Success',
        'Order deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );
      return true;
    } catch (e) {
      debugPrint('Error deleting order: $e');
      Get.snackbar(
        'Error',
        'Failed to delete order',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Order Status Management
  Future<bool> updateOrderStatus(
    String orderId,
    OrderStatus status, {
    String? notes,
  }) async {
    try {
      final order = _orders.firstWhere((o) => o.id == orderId);
      final updatedOrder = order.copyWith(
        status: status,
        adminNotes: notes,
        actualDeliveryDate:
            status == OrderStatus.delivered ? DateTime.now() : null,
        cancelledAt: status == OrderStatus.cancelled ? DateTime.now() : null,
        updatedAt: DateTime.now(),
      );

      return await updateOrder(updatedOrder);
    } catch (e) {
      debugPrint('Error updating order status: $e');
      return false;
    }
  }

  // Order Assignment System
  Future<bool> assignOrderToFieldMan(
    String orderId,
    String fieldManId, {
    String? notes,
  }) async {
    try {
      final order = _orders.firstWhere((o) => o.id == orderId);
      final fieldMan = _fieldStaff.firstWhere((f) => f.id == fieldManId);

      final assignment = OrderAssignment(
        assignmentId: DateTime.now().millisecondsSinceEpoch.toString(),
        fieldManId: fieldManId,
        fieldManName: fieldMan.name,
        assignedAt: DateTime.now(),
        assignedBy: 'admin', // TODO: Get current admin user
        notes: notes,
      );

      final updatedOrder = order.copyWith(
        fieldManId: fieldManId,
        fieldManName: fieldMan.name,
        status: OrderStatus.confirmed,
        assignmentHistory: [...order.assignmentHistory, assignment],
        updatedAt: DateTime.now(),
      );

      final success = await updateOrder(updatedOrder);
      if (success) {
        Get.snackbar(
          'Success',
          'Order assigned to ${fieldMan.name}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: AppColors.textOnPrimary,
        );
      }
      return success;
    } catch (e) {
      debugPrint('Error assigning order: $e');
      Get.snackbar(
        'Error',
        'Failed to assign order',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    }
  }

  Future<bool> reassignOrder(
    String orderId,
    String newFieldManId, {
    String? reason,
  }) async {
    try {
      final order = _orders.firstWhere((o) => o.id == orderId);
      final newFieldMan = _fieldStaff.firstWhere((f) => f.id == newFieldManId);

      // Mark current assignment as inactive
      final updatedHistory =
          order.assignmentHistory.map((assignment) {
            if (assignment.isActive) {
              return OrderAssignment(
                assignmentId: assignment.assignmentId,
                fieldManId: assignment.fieldManId,
                fieldManName: assignment.fieldManName,
                assignedAt: assignment.assignedAt,
                assignedBy: assignment.assignedBy,
                notes: assignment.notes,
                isActive: false,
              );
            }
            return assignment;
          }).toList();

      // Add new assignment
      final newAssignment = OrderAssignment(
        assignmentId: DateTime.now().millisecondsSinceEpoch.toString(),
        fieldManId: newFieldManId,
        fieldManName: newFieldMan.name,
        assignedAt: DateTime.now(),
        assignedBy: 'admin', // TODO: Get current admin user
        notes: reason,
      );

      final updatedOrder = order.copyWith(
        fieldManId: newFieldManId,
        fieldManName: newFieldMan.name,
        assignmentHistory: [...updatedHistory, newAssignment],
        updatedAt: DateTime.now(),
      );

      return await updateOrder(updatedOrder);
    } catch (e) {
      debugPrint('Error reassigning order: $e');
      return false;
    }
  }

  // Bulk Operations
  Future<bool> bulkUpdateStatus(
    List<String> orderIds,
    OrderStatus status,
  ) async {
    try {
      _isLoading.value = true;
      int successCount = 0;

      for (String orderId in orderIds) {
        final success = await updateOrderStatus(orderId, status);
        if (success) successCount++;
      }

      Get.snackbar(
        'Bulk Update',
        'Updated $successCount of ${orderIds.length} orders',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor:
            successCount == orderIds.length
                ? AppColors.success
                : AppColors.warning,
        colorText: AppColors.textOnPrimary,
      );

      return successCount > 0;
    } catch (e) {
      debugPrint('Error in bulk update: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> bulkAssignToFieldMan(
    List<String> orderIds,
    String fieldManId,
  ) async {
    try {
      _isLoading.value = true;
      int successCount = 0;

      for (String orderId in orderIds) {
        final success = await assignOrderToFieldMan(orderId, fieldManId);
        if (success) successCount++;
      }

      Get.snackbar(
        'Bulk Assignment',
        'Assigned $successCount of ${orderIds.length} orders',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor:
            successCount == orderIds.length
                ? AppColors.success
                : AppColors.warning,
        colorText: AppColors.textOnPrimary,
      );

      return successCount > 0;
    } catch (e) {
      debugPrint('Error in bulk assignment: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Filtering and Searching
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  void setStatusFilter(String status) {
    _selectedStatus.value = status;
    _applyFilters();
  }

  void setPriorityFilter(String priority) {
    _selectedPriority.value = priority;
    _applyFilters();
  }

  void setCategoryFilter(String category) {
    _selectedCategory.value = category;
    _applyFilters();
  }

  void setFieldManFilter(String fieldManId) {
    _selectedFieldMan.value = fieldManId;
    _applyFilters();
  }

  void setDateRange(DateRange? range) {
    _dateRange.value = range;
    _applyFilters();
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedStatus.value = 'all';
    _selectedPriority.value = 'all';
    _selectedCategory.value = 'all';
    _selectedFieldMan.value = 'all';
    _dateRange.value = null;
    _applyFilters();
  }

  void _applyFilters() {
    List<Order> filtered = List.from(_orders);

    // Search filter
    if (_searchQuery.value.isNotEmpty) {
      filtered =
          filtered.where((order) {
            return order.customerName.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                order.id.toLowerCase().contains(
                  _searchQuery.value.toLowerCase(),
                ) ||
                order.notes?.toLowerCase().contains(
                      _searchQuery.value.toLowerCase(),
                    ) ==
                    true;
          }).toList();
    }

    // Status filter
    if (_selectedStatus.value != 'all') {
      final status = OrderStatus.values.firstWhere(
        (s) => s.name == _selectedStatus.value,
        orElse: () => OrderStatus.pending,
      );
      filtered = filtered.where((order) => order.status == status).toList();
    }

    // Priority filter
    if (_selectedPriority.value != 'all') {
      final priority = OrderPriority.values.firstWhere(
        (p) => p.name == _selectedPriority.value,
        orElse: () => OrderPriority.normal,
      );
      filtered = filtered.where((order) => order.priority == priority).toList();
    }

    // Category filter
    if (_selectedCategory.value != 'all') {
      filtered =
          filtered.where((order) {
            return order.items.any(
              (item) => item.productCategory == _selectedCategory.value,
            );
          }).toList();
    }

    // Field man filter
    if (_selectedFieldMan.value != 'all') {
      filtered =
          filtered
              .where((order) => order.fieldManId == _selectedFieldMan.value)
              .toList();
    }

    // Date range filter
    if (_dateRange.value != null) {
      filtered =
          filtered.where((order) {
            return _dateRange.value!.contains(order.orderDate);
          }).toList();
    }

    // Apply sorting
    _applySorting(filtered);
  }

  // Sorting
  void setSorting(String sortField, bool ascending) {
    _sortBy.value = sortField;
    _sortAscending.value = ascending;
    _applyFilters();
  }

  void _applySorting(List<Order> orders) {
    orders.sort((a, b) {
      int comparison = 0;

      switch (_sortBy.value) {
        case 'orderDate':
          comparison = a.orderDate.compareTo(b.orderDate);
          break;
        case 'customerName':
          comparison = a.customerName.compareTo(b.customerName);
          break;
        case 'totalAmount':
          comparison = a.totalAmount.compareTo(b.totalAmount);
          break;
        case 'status':
          comparison = a.status.index.compareTo(b.status.index);
          break;
        case 'priority':
          comparison = a.priority.index.compareTo(b.priority.index);
          break;
        case 'deliveryDate':
          final aDate = a.deliveryDate ?? DateTime(2099);
          final bDate = b.deliveryDate ?? DateTime(2099);
          comparison = aDate.compareTo(bDate);
          break;
        default:
          comparison = a.orderDate.compareTo(b.orderDate);
      }

      return _sortAscending.value ? comparison : -comparison;
    });

    _filteredOrders.assignAll(orders);
  }

  // Quick access methods
  List<Order> getTodaysOrders() {
    final today = DateTime.now();
    return _orders.where((order) {
      return order.orderDate.year == today.year &&
          order.orderDate.month == today.month &&
          order.orderDate.day == today.day;
    }).toList();
  }

  List<Order> getPendingOrders() {
    return _orders
        .where((order) => order.status == OrderStatus.pending)
        .toList();
  }

  List<Order> getOverdueOrders() {
    return _orders.where((order) => order.isOverdue).toList();
  }

  List<Order> getUnassignedOrders() {
    return _orders
        .where((order) => !order.isAssigned && order.canBeAssigned())
        .toList();
  }

  List<Order> getOrdersByFieldMan(String fieldManId) {
    return _orders.where((order) => order.fieldManId == fieldManId).toList();
  }

  List<Order> getOrdersByCustomer(String customerId) {
    return _orders.where((order) => order.customerId == customerId).toList();
  }

  List<Order> getHighPriorityOrders() {
    return _orders
        .where(
          (order) =>
              order.priority == OrderPriority.high ||
              order.priority == OrderPriority.urgent,
        )
        .toList();
  }
}
