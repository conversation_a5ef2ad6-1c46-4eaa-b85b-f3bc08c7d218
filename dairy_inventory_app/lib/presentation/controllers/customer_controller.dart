import 'package:get/get.dart';
import '../../models/user.dart';
import '../../services/database_service.dart';

class CustomerController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();

  // Observables
  final RxList<User> _customers = <User>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;

  // Getters
  List<User> get customers => _customers;
  bool get isLoading => _isLoading.value;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    loadCustomers();
  }

  Future<void> loadCustomers() async {
    try {
      _isLoading.value = true;
      final customerList = await _databaseService.getCustomers();
      _customers.assignAll(customerList);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load customers',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> addCustomer(User customer) async {
    try {
      _isLoading.value = true;
      await _databaseService.insertUser(customer);
      _customers.add(customer);

      Get.snackbar(
        'Success',
        'Customer added successfully',
        snackPosition: SnackPosition.BOTTOM,
      );

      return true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to add customer',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateCustomer(User customer) async {
    try {
      final index = _customers.indexWhere((c) => c.id == customer.id);
      if (index != -1) {
        _customers[index] = customer;

        Get.snackbar(
          'Success',
          'Customer updated successfully',
          snackPosition: SnackPosition.BOTTOM,
        );

        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update customer',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }

  Future<bool> deleteCustomer(String customerId) async {
    try {
      final index = _customers.indexWhere((c) => c.id == customerId);
      if (index != -1) {
        _customers.removeAt(index);

        Get.snackbar(
          'Success',
          'Customer deleted successfully',
          snackPosition: SnackPosition.BOTTOM,
        );

        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to delete customer',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }

  // Search and filter methods
  void setSearchQuery(String query) {
    _searchQuery.value = query;
  }

  List<User> getFilteredCustomers() {
    if (_searchQuery.value.isEmpty) {
      return _customers;
    }

    final query = _searchQuery.value.toLowerCase();
    return _customers.where((customer) {
      return customer.name.toLowerCase().contains(query) ||
          customer.email.toLowerCase().contains(query) ||
          customer.phone.toLowerCase().contains(query);
    }).toList();
  }

  User? getCustomerById(String id) {
    try {
      return _customers.firstWhere((customer) => customer.id == id);
    } catch (e) {
      return null;
    }
  }

  List<User> getActiveCustomers() {
    return _customers.where((customer) => customer.isActive).toList();
  }

  List<User> getCustomersByLocation(String location) {
    return _customers
        .where(
          (customer) =>
              customer.address?.toLowerCase().contains(
                location.toLowerCase(),
              ) ??
              false,
        )
        .toList();
  }

  int getTotalCustomersCount() => _customers.length;

  int getActiveCustomersCount() => getActiveCustomers().length;

  // Analytics methods
  Map<String, int> getCustomersByMonth() {
    final Map<String, int> monthlyCustomers = {};
    final now = DateTime.now();

    for (int i = 11; i >= 0; i--) {
      final month = now.month - i;
      final year = month <= 0 ? now.year - 1 : now.year;
      final adjustedMonth = month <= 0 ? month + 12 : month;

      final monthKey = '$adjustedMonth/$year';

      final monthCustomers =
          _customers.where((customer) {
            return customer.createdAt.year == year &&
                customer.createdAt.month == adjustedMonth;
          }).length;

      monthlyCustomers[monthKey] = monthCustomers;
    }

    return monthlyCustomers;
  }

  List<User> getNewCustomersThisMonth() {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);

    return _customers.where((customer) {
      return customer.createdAt.isAfter(monthStart);
    }).toList();
  }

  List<User> getTopCustomers({int limit = 10}) {
    // This would typically be based on order value/frequency
    // For now, returning most recent customers
    final sortedCustomers = List<User>.from(_customers);
    sortedCustomers.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedCustomers.take(limit).toList();
  }
}
