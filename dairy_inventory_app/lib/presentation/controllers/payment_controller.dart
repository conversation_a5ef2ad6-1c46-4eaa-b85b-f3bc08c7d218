import 'package:get/get.dart';
import '../../models/payment.dart';
import '../../services/database_service.dart';

class PaymentController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();
  
  // Observables
  final RxList<Payment> _payments = <Payment>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _selectedFilter = 'all'.obs;
  
  // Getters
  List<Payment> get payments => _payments;
  bool get isLoading => _isLoading.value;
  String get selectedFilter => _selectedFilter.value;
  
  @override
  void onInit() {
    super.onInit();
    loadPayments();
  }
  
  Future<void> loadPayments({String? customerId, String? orderId}) async {
    try {
      _isLoading.value = true;
      final paymentList = await _databaseService.getPayments(
        customerId: customerId,
        orderId: orderId,
      );
      _payments.assignAll(paymentList);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load payments',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<bool> addPayment(Payment payment) async {
    try {
      _isLoading.value = true;
      await _databaseService.insertPayment(payment);
      _payments.insert(0, payment);
      
      Get.snackbar(
        'Success',
        'Payment recorded successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to record payment',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<bool> processPayment({
    required String orderId,
    required String customerId,
    required String customerName,
    String? fieldManId,
    String? fieldManName,
    required double amount,
    required PaymentMethod method,
    String? transactionId,
    String? notes,
  }) async {
    final payment = Payment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      orderId: orderId,
      customerId: customerId,
      customerName: customerName,
      fieldManId: fieldManId,
      fieldManName: fieldManName,
      amount: amount,
      method: method,
      transactionId: transactionId,
      notes: notes,
      paymentDate: DateTime.now(),
      createdAt: DateTime.now(),
    );
    
    return await addPayment(payment);
  }
  
  // Filter methods
  void setFilter(String filter) {
    _selectedFilter.value = filter;
  }
  
  List<Payment> getFilteredPayments() {
    switch (_selectedFilter.value) {
      case 'cash':
        return _payments.where((payment) => payment.method == PaymentMethod.cash).toList();
      case 'online':
        return _payments.where((payment) => payment.method == PaymentMethod.online).toList();
      case 'upi':
        return _payments.where((payment) => payment.method == PaymentMethod.upi).toList();
      case 'today':
        final today = DateTime.now();
        return _payments.where((payment) {
          return payment.paymentDate.year == today.year &&
                 payment.paymentDate.month == today.month &&
                 payment.paymentDate.day == today.day;
        }).toList();
      default:
        return _payments;
    }
  }
  
  List<Payment> getPaymentsByCustomer(String customerId) {
    return _payments.where((payment) => payment.customerId == customerId).toList();
  }
  
  List<Payment> getPaymentsByOrder(String orderId) {
    return _payments.where((payment) => payment.orderId == orderId).toList();
  }
  
  List<Payment> getTodaysPayments() {
    final today = DateTime.now();
    return _payments.where((payment) {
      return payment.paymentDate.year == today.year &&
             payment.paymentDate.month == today.month &&
             payment.paymentDate.day == today.day;
    }).toList();
  }
  
  List<Payment> getPaymentsByDateRange(DateTime startDate, DateTime endDate) {
    return _payments.where((payment) {
      return payment.paymentDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
             payment.paymentDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }
  
  double getTotalPaymentsAmount() {
    return _payments.fold(0.0, (sum, payment) => sum + payment.amount);
  }
  
  double getTodaysCollection() {
    final todaysPayments = getTodaysPayments();
    return todaysPayments.fold(0.0, (sum, payment) => sum + payment.amount);
  }
  
  double getMonthlyCollection() {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    final monthEnd = DateTime(now.year, now.month + 1, 0);
    
    final monthlyPayments = getPaymentsByDateRange(monthStart, monthEnd);
    return monthlyPayments.fold(0.0, (sum, payment) => sum + payment.amount);
  }
  
  Map<PaymentMethod, double> getPaymentsByMethod() {
    final Map<PaymentMethod, double> methodTotals = {};
    
    for (final payment in _payments) {
      methodTotals[payment.method] = (methodTotals[payment.method] ?? 0) + payment.amount;
    }
    
    return methodTotals;
  }
  
  List<Payment> getCashPayments() {
    return _payments.where((payment) => payment.method == PaymentMethod.cash).toList();
  }
  
  double getCashCollectionTotal() {
    final cashPayments = getCashPayments();
    return cashPayments.fold(0.0, (sum, payment) => sum + payment.amount);
  }
  
  // Analytics methods
  Map<String, double> getDailyCollectionForWeek() {
    final Map<String, double> dailyCollection = {};
    final now = DateTime.now();
    
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayKey = '${date.day}/${date.month}';
      
      final dayPayments = _payments.where((payment) {
        return payment.paymentDate.year == date.year &&
               payment.paymentDate.month == date.month &&
               payment.paymentDate.day == date.day;
      }).toList();
      
      dailyCollection[dayKey] = dayPayments.fold(0.0, (sum, payment) => sum + payment.amount);
    }
    
    return dailyCollection;
  }
  
  Map<String, double> getMonthlyCollectionForYear() {
    final Map<String, double> monthlyCollection = {};
    final now = DateTime.now();
    
    for (int i = 11; i >= 0; i--) {
      final month = now.month - i;
      final year = month <= 0 ? now.year - 1 : now.year;
      final adjustedMonth = month <= 0 ? month + 12 : month;
      
      final monthKey = '$adjustedMonth/$year';
      
      final monthPayments = _payments.where((payment) {
        return payment.paymentDate.year == year &&
               payment.paymentDate.month == adjustedMonth;
      }).toList();
      
      monthlyCollection[monthKey] = monthPayments.fold(0.0, (sum, payment) => sum + payment.amount);
    }
    
    return monthlyCollection;
  }
}
