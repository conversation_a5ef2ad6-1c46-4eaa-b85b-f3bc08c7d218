import 'package:get/get.dart';
import '../../models/milk_product.dart';
import '../../services/database_service.dart';

class ProductController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();
  
  // Observables
  final RxList<MilkProduct> _products = <MilkProduct>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _selectedCategory = 'all'.obs;
  
  // Getters
  List<MilkProduct> get products => _products;
  bool get isLoading => _isLoading.value;
  String get selectedCategory => _selectedCategory.value;
  
  @override
  void onInit() {
    super.onInit();
    loadProducts();
  }
  
  Future<void> loadProducts() async {
    try {
      _isLoading.value = true;
      final productList = await _databaseService.getMilkProducts();
      _products.assignAll(productList.where((p) => p.isActive).toList());
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load products',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<bool> addProduct(MilkProduct product) async {
    try {
      _isLoading.value = true;
      await _databaseService.insertMilkProduct(product);
      _products.add(product);
      
      Get.snackbar(
        'Success',
        'Product added successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to add product',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<bool> updateProduct(MilkProduct product) async {
    try {
      final index = _products.indexWhere((p) => p.id == product.id);
      if (index != -1) {
        _products[index] = product;
        
        Get.snackbar(
          'Success',
          'Product updated successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
        
        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update product',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }
  
  Future<bool> deleteProduct(String productId) async {
    try {
      final index = _products.indexWhere((p) => p.id == productId);
      if (index != -1) {
        final updatedProduct = _products[index].copyWith(isActive: false);
        _products[index] = updatedProduct;
        
        Get.snackbar(
          'Success',
          'Product deleted successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
        
        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to delete product',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }
  
  // Filter methods
  void setCategory(String category) {
    _selectedCategory.value = category;
  }
  
  List<MilkProduct> getFilteredProducts() {
    if (_selectedCategory.value == 'all') {
      return _products;
    }
    
    final category = MilkCategory.values.firstWhere(
      (cat) => cat.name.toLowerCase() == _selectedCategory.value.toLowerCase(),
      orElse: () => MilkCategory.fullCream,
    );
    
    return _products.where((product) => product.category == category).toList();
  }
  
  List<MilkProduct> getProductsByCategory(MilkCategory category) {
    return _products.where((product) => product.category == category).toList();
  }
  
  MilkProduct? getProductById(String id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }
  
  List<MilkProduct> searchProducts(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _products.where((product) {
      return product.name.toLowerCase().contains(lowercaseQuery) ||
             product.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
  
  List<String> get categories {
    return ['all'] + MilkCategory.values.map((cat) => cat.name).toList();
  }
  
  double getAveragePrice() {
    if (_products.isEmpty) return 0.0;
    return _products.fold(0.0, (sum, product) => sum + product.pricePerLiter) / _products.length;
  }
  
  int getProductCount() => _products.length;
  
  Map<MilkCategory, int> getCategoryDistribution() {
    final Map<MilkCategory, int> distribution = {};
    for (final product in _products) {
      distribution[product.category] = (distribution[product.category] ?? 0) + 1;
    }
    return distribution;
  }
}
