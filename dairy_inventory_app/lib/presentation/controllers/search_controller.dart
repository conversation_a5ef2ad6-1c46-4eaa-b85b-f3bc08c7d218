import 'package:get/get.dart';
import '../../models/order.dart';
import '../../models/user.dart';
import '../../models/milk_product.dart';
import '../../models/payment.dart';
import 'order_controller.dart';
import 'customer_controller.dart';
import 'product_controller.dart';
import 'payment_controller.dart';

enum SearchType { all, orders, customers, products, payments }
enum SortBy { date, amount, name, status }
enum SortOrder { ascending, descending }

class SearchController extends GetxController {
  // Observables
  final RxString _searchQuery = ''.obs;
  final Rx<SearchType> _searchType = SearchType.all.obs;
  final Rx<SortBy> _sortBy = SortBy.date.obs;
  final Rx<SortOrder> _sortOrder = SortOrder.descending.obs;
  final RxBool _isLoading = false.obs;
  final Rx<DateTime?> _startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> _endDate = Rx<DateTime?>(null);
  
  // Search results
  final RxList<Order> _orderResults = <Order>[].obs;
  final RxList<User> _customerResults = <User>[].obs;
  final RxList<MilkProduct> _productResults = <MilkProduct>[].obs;
  final RxList<Payment> _paymentResults = <Payment>[].obs;
  
  // Getters
  String get searchQuery => _searchQuery.value;
  SearchType get searchType => _searchType.value;
  SortBy get sortBy => _sortBy.value;
  SortOrder get sortOrder => _sortOrder.value;
  bool get isLoading => _isLoading.value;
  DateTime? get startDate => _startDate.value;
  DateTime? get endDate => _endDate.value;
  
  List<Order> get orderResults => _orderResults;
  List<User> get customerResults => _customerResults;
  List<MilkProduct> get productResults => _productResults;
  List<Payment> get paymentResults => _paymentResults;
  
  // Controllers
  late OrderController _orderController;
  late CustomerController _customerController;
  late ProductController _productController;
  late PaymentController _paymentController;
  
  @override
  void onInit() {
    super.onInit();
    _initializeControllers();
    
    // Listen to search query changes
    debounce(_searchQuery, (_) => performSearch(), time: const Duration(milliseconds: 500));
  }
  
  void _initializeControllers() {
    try {
      _orderController = Get.find<OrderController>();
    } catch (e) {
      _orderController = Get.put(OrderController());
    }
    
    try {
      _customerController = Get.find<CustomerController>();
    } catch (e) {
      _customerController = Get.put(CustomerController());
    }
    
    try {
      _productController = Get.find<ProductController>();
    } catch (e) {
      _productController = Get.put(ProductController());
    }
    
    try {
      _paymentController = Get.find<PaymentController>();
    } catch (e) {
      _paymentController = Get.put(PaymentController());
    }
  }
  
  void setSearchQuery(String query) {
    _searchQuery.value = query;
  }
  
  void setSearchType(SearchType type) {
    _searchType.value = type;
    performSearch();
  }
  
  void setSortBy(SortBy sortBy) {
    _sortBy.value = sortBy;
    _applySorting();
  }
  
  void setSortOrder(SortOrder order) {
    _sortOrder.value = order;
    _applySorting();
  }
  
  void setDateRange(DateTime? start, DateTime? end) {
    _startDate.value = start;
    _endDate.value = end;
    performSearch();
  }
  
  void clearDateRange() {
    _startDate.value = null;
    _endDate.value = null;
    performSearch();
  }
  
  Future<void> performSearch() async {
    if (_searchQuery.value.isEmpty && _startDate.value == null && _endDate.value == null) {
      _clearResults();
      return;
    }
    
    _isLoading.value = true;
    
    try {
      switch (_searchType.value) {
        case SearchType.all:
          await _searchAll();
          break;
        case SearchType.orders:
          await _searchOrders();
          break;
        case SearchType.customers:
          await _searchCustomers();
          break;
        case SearchType.products:
          await _searchProducts();
          break;
        case SearchType.payments:
          await _searchPayments();
          break;
      }
      
      _applySorting();
    } catch (e) {
      Get.snackbar(
        'Search Error',
        'Failed to perform search',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<void> _searchAll() async {
    await _searchOrders();
    await _searchCustomers();
    await _searchProducts();
    await _searchPayments();
  }
  
  Future<void> _searchOrders() async {
    final query = _searchQuery.value.toLowerCase();
    final orders = _orderController.orders;
    
    _orderResults.value = orders.where((order) {
      final matchesQuery = query.isEmpty || 
          order.customerName.toLowerCase().contains(query) ||
          order.id.toLowerCase().contains(query) ||
          order.status.name.toLowerCase().contains(query);
      
      final matchesDateRange = _isWithinDateRange(order.orderDate);
      
      return matchesQuery && matchesDateRange;
    }).toList();
  }
  
  Future<void> _searchCustomers() async {
    final query = _searchQuery.value.toLowerCase();
    final customers = _customerController.customers;
    
    _customerResults.value = customers.where((customer) {
      final matchesQuery = query.isEmpty ||
          customer.name.toLowerCase().contains(query) ||
          customer.email.toLowerCase().contains(query) ||
          customer.phone.toLowerCase().contains(query) ||
          customer.address.toLowerCase().contains(query);
      
      final matchesDateRange = _isWithinDateRange(customer.createdAt);
      
      return matchesQuery && matchesDateRange;
    }).toList();
  }
  
  Future<void> _searchProducts() async {
    final query = _searchQuery.value.toLowerCase();
    final products = _productController.products;
    
    _productResults.value = products.where((product) {
      final matchesQuery = query.isEmpty ||
          product.name.toLowerCase().contains(query) ||
          product.description.toLowerCase().contains(query) ||
          product.category.name.toLowerCase().contains(query);
      
      final matchesDateRange = _isWithinDateRange(product.createdAt);
      
      return matchesQuery && matchesDateRange;
    }).toList();
  }
  
  Future<void> _searchPayments() async {
    final query = _searchQuery.value.toLowerCase();
    final payments = _paymentController.payments;
    
    _paymentResults.value = payments.where((payment) {
      final matchesQuery = query.isEmpty ||
          payment.customerName.toLowerCase().contains(query) ||
          payment.orderId.toLowerCase().contains(query) ||
          payment.method.name.toLowerCase().contains(query) ||
          (payment.transactionId?.toLowerCase().contains(query) ?? false);
      
      final matchesDateRange = _isWithinDateRange(payment.paymentDate);
      
      return matchesQuery && matchesDateRange;
    }).toList();
  }
  
  bool _isWithinDateRange(DateTime date) {
    if (_startDate.value == null && _endDate.value == null) return true;
    
    if (_startDate.value != null && date.isBefore(_startDate.value!)) {
      return false;
    }
    
    if (_endDate.value != null && date.isAfter(_endDate.value!.add(const Duration(days: 1)))) {
      return false;
    }
    
    return true;
  }
  
  void _applySorting() {
    switch (_searchType.value) {
      case SearchType.all:
        _sortAllResults();
        break;
      case SearchType.orders:
        _sortOrders();
        break;
      case SearchType.customers:
        _sortCustomers();
        break;
      case SearchType.products:
        _sortProducts();
        break;
      case SearchType.payments:
        _sortPayments();
        break;
    }
  }
  
  void _sortAllResults() {
    _sortOrders();
    _sortCustomers();
    _sortProducts();
    _sortPayments();
  }
  
  void _sortOrders() {
    final orders = List<Order>.from(_orderResults);
    
    switch (_sortBy.value) {
      case SortBy.date:
        orders.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.orderDate.compareTo(b.orderDate)
            : b.orderDate.compareTo(a.orderDate));
        break;
      case SortBy.amount:
        orders.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.totalAmount.compareTo(b.totalAmount)
            : b.totalAmount.compareTo(a.totalAmount));
        break;
      case SortBy.name:
        orders.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.customerName.compareTo(b.customerName)
            : b.customerName.compareTo(a.customerName));
        break;
      case SortBy.status:
        orders.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.status.index.compareTo(b.status.index)
            : b.status.index.compareTo(a.status.index));
        break;
    }
    
    _orderResults.value = orders;
  }
  
  void _sortCustomers() {
    final customers = List<User>.from(_customerResults);
    
    switch (_sortBy.value) {
      case SortBy.date:
        customers.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.createdAt.compareTo(b.createdAt)
            : b.createdAt.compareTo(a.createdAt));
        break;
      case SortBy.name:
        customers.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.name.compareTo(b.name)
            : b.name.compareTo(a.name));
        break;
      default:
        customers.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.name.compareTo(b.name)
            : b.name.compareTo(a.name));
        break;
    }
    
    _customerResults.value = customers;
  }
  
  void _sortProducts() {
    final products = List<MilkProduct>.from(_productResults);
    
    switch (_sortBy.value) {
      case SortBy.date:
        products.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.createdAt.compareTo(b.createdAt)
            : b.createdAt.compareTo(a.createdAt));
        break;
      case SortBy.amount:
        products.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.pricePerLiter.compareTo(b.pricePerLiter)
            : b.pricePerLiter.compareTo(a.pricePerLiter));
        break;
      case SortBy.name:
        products.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.name.compareTo(b.name)
            : b.name.compareTo(a.name));
        break;
      default:
        products.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.name.compareTo(b.name)
            : b.name.compareTo(a.name));
        break;
    }
    
    _productResults.value = products;
  }
  
  void _sortPayments() {
    final payments = List<Payment>.from(_paymentResults);
    
    switch (_sortBy.value) {
      case SortBy.date:
        payments.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.paymentDate.compareTo(b.paymentDate)
            : b.paymentDate.compareTo(a.paymentDate));
        break;
      case SortBy.amount:
        payments.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.amount.compareTo(b.amount)
            : b.amount.compareTo(a.amount));
        break;
      case SortBy.name:
        payments.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.customerName.compareTo(b.customerName)
            : b.customerName.compareTo(a.customerName));
        break;
      default:
        payments.sort((a, b) => _sortOrder.value == SortOrder.ascending
            ? a.paymentDate.compareTo(b.paymentDate)
            : b.paymentDate.compareTo(a.paymentDate));
        break;
    }
    
    _paymentResults.value = payments;
  }
  
  void _clearResults() {
    _orderResults.clear();
    _customerResults.clear();
    _productResults.clear();
    _paymentResults.clear();
  }
  
  void clearSearch() {
    _searchQuery.value = '';
    _startDate.value = null;
    _endDate.value = null;
    _clearResults();
  }
  
  int getTotalResultsCount() {
    return _orderResults.length + 
           _customerResults.length + 
           _productResults.length + 
           _paymentResults.length;
  }
}
