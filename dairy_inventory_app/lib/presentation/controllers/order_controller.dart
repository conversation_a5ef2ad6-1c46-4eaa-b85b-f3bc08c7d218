import 'package:get/get.dart';
import '../../models/order.dart';
import '../../services/database_service.dart';

class OrderController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();
  
  // Observables
  final RxList<Order> _orders = <Order>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _selectedFilter = 'all'.obs;
  
  // Getters
  List<Order> get orders => _orders;
  bool get isLoading => _isLoading.value;
  String get selectedFilter => _selectedFilter.value;
  
  @override
  void onInit() {
    super.onInit();
    loadOrders();
  }
  
  Future<void> loadOrders({String? customerId, String? fieldManId}) async {
    try {
      _isLoading.value = true;
      final orderList = await _databaseService.getOrders(
        customerId: customerId,
        fieldManId: fieldManId,
      );
      _orders.assignAll(orderList);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load orders',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<bool> createOrder(Order order) async {
    try {
      _isLoading.value = true;
      await _databaseService.insertOrder(order);
      _orders.insert(0, order);
      
      Get.snackbar(
        'Success',
        'Order created successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to create order',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<bool> updateOrderStatus(String orderId, OrderStatus status) async {
    try {
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex == -1) return false;
      
      final updatedOrder = Order(
        id: _orders[orderIndex].id,
        customerId: _orders[orderIndex].customerId,
        customerName: _orders[orderIndex].customerName,
        fieldManId: _orders[orderIndex].fieldManId,
        fieldManName: _orders[orderIndex].fieldManName,
        items: _orders[orderIndex].items,
        totalAmount: _orders[orderIndex].totalAmount,
        paidAmount: _orders[orderIndex].paidAmount,
        pendingAmount: _orders[orderIndex].pendingAmount,
        status: status,
        paymentStatus: _orders[orderIndex].paymentStatus,
        orderDate: _orders[orderIndex].orderDate,
        deliveryDate: status == OrderStatus.delivered 
            ? DateTime.now() 
            : _orders[orderIndex].deliveryDate,
        notes: _orders[orderIndex].notes,
        isRecurring: _orders[orderIndex].isRecurring,
        createdAt: _orders[orderIndex].createdAt,
        updatedAt: DateTime.now(),
      );
      
      _orders[orderIndex] = updatedOrder;
      
      Get.snackbar(
        'Success',
        'Order status updated',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update order status',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }
  
  Future<bool> assignFieldMan(String orderId, String fieldManId, String fieldManName) async {
    try {
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex == -1) return false;
      
      final updatedOrder = Order(
        id: _orders[orderIndex].id,
        customerId: _orders[orderIndex].customerId,
        customerName: _orders[orderIndex].customerName,
        fieldManId: fieldManId,
        fieldManName: fieldManName,
        items: _orders[orderIndex].items,
        totalAmount: _orders[orderIndex].totalAmount,
        paidAmount: _orders[orderIndex].paidAmount,
        pendingAmount: _orders[orderIndex].pendingAmount,
        status: _orders[orderIndex].status,
        paymentStatus: _orders[orderIndex].paymentStatus,
        orderDate: _orders[orderIndex].orderDate,
        deliveryDate: _orders[orderIndex].deliveryDate,
        notes: _orders[orderIndex].notes,
        isRecurring: _orders[orderIndex].isRecurring,
        createdAt: _orders[orderIndex].createdAt,
        updatedAt: DateTime.now(),
      );
      
      _orders[orderIndex] = updatedOrder;
      
      Get.snackbar(
        'Success',
        'Field staff assigned successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to assign field staff',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }
  
  // Filter methods
  void setFilter(String filter) {
    _selectedFilter.value = filter;
  }
  
  List<Order> getFilteredOrders() {
    switch (_selectedFilter.value) {
      case 'pending':
        return _orders.where((order) => order.status == OrderStatus.pending).toList();
      case 'confirmed':
        return _orders.where((order) => order.status == OrderStatus.confirmed).toList();
      case 'delivered':
        return _orders.where((order) => order.status == OrderStatus.delivered).toList();
      case 'today':
        final today = DateTime.now();
        return _orders.where((order) {
          return order.orderDate.year == today.year &&
                 order.orderDate.month == today.month &&
                 order.orderDate.day == today.day;
        }).toList();
      default:
        return _orders;
    }
  }
  
  List<Order> getOrdersByStatus(OrderStatus status) {
    return _orders.where((order) => order.status == status).toList();
  }
  
  List<Order> getTodaysOrders() {
    final today = DateTime.now();
    return _orders.where((order) {
      return order.orderDate.year == today.year &&
             order.orderDate.month == today.month &&
             order.orderDate.day == today.day;
    }).toList();
  }
  
  double getTotalRevenue() {
    return _orders.fold(0.0, (sum, order) => sum + order.totalAmount);
  }
  
  double getPendingAmount() {
    return _orders.fold(0.0, (sum, order) => sum + order.pendingAmount);
  }
  
  double getTodaysRevenue() {
    final todaysOrders = getTodaysOrders();
    return todaysOrders.fold(0.0, (sum, order) => sum + order.totalAmount);
  }
  
  int getPendingOrdersCount() {
    return getOrdersByStatus(OrderStatus.pending).length;
  }
  
  int getDeliveredOrdersCount() {
    return getOrdersByStatus(OrderStatus.delivered).length;
  }
}
