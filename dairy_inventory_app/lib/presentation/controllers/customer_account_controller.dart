import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../../models/billing.dart';
import '../../models/payment_collection.dart';
import '../../models/user.dart';
import '../../models/analytics.dart';
import '../../services/database_service.dart';
import '../../core/constants/app_colors.dart';

class CustomerAccountController extends GetxController {
  final DatabaseService _databaseService = DatabaseService();

  // Observables
  final RxList<Bill> _customerBills = <Bill>[].obs;
  final RxList<PaymentCollection> _paymentHistory = <PaymentCollection>[].obs;
  final RxList<PaymentReminder> _reminders = <PaymentReminder>[].obs;
  final RxList<User> _customers = <User>[].obs;
  final RxList<User> _blockedCustomers = <User>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedCustomer = 'all'.obs;
  final RxString _selectedAging = 'all'.obs;
  final RxString _sortBy = 'pendingAmount'.obs;
  final RxBool _sortAscending = false.obs;

  // Settings
  final RxInt _paymentGracePeriod = 30.obs; // days
  final RxDouble _blockingThreshold = 5000.0.obs; // amount
  final RxBool _autoRemindersEnabled = true.obs;
  final RxInt _reminderFrequency = 7.obs; // days

  // Getters
  List<Bill> get customerBills => _customerBills;
  List<PaymentCollection> get paymentHistory => _paymentHistory;
  List<PaymentReminder> get reminders => _reminders;
  List<User> get customers => _customers;
  List<User> get blockedCustomers => _blockedCustomers;
  bool get isLoading => _isLoading.value;
  String get searchQuery => _searchQuery.value;
  String get selectedCustomer => _selectedCustomer.value;
  String get selectedAging => _selectedAging.value;
  String get sortBy => _sortBy.value;
  bool get sortAscending => _sortAscending.value;
  int get paymentGracePeriod => _paymentGracePeriod.value;
  double get blockingThreshold => _blockingThreshold.value;
  bool get autoRemindersEnabled => _autoRemindersEnabled.value;
  int get reminderFrequency => _reminderFrequency.value;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    try {
      _isLoading.value = true;
      await Future.wait([
        loadCustomerBills(),
        loadPaymentHistory(),
        loadReminders(),
        loadCustomers(),
      ]);
      _updateBlockedCustomers();
    } catch (e) {
      debugPrint('Error loading initial data: $e');
      Get.snackbar(
        'Error',
        'Failed to load customer account data',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadCustomerBills() async {
    try {
      final bills = await _databaseService.getBills();
      _customerBills.assignAll(bills);
    } catch (e) {
      debugPrint('Error loading customer bills: $e');
      rethrow;
    }
  }

  Future<void> loadPaymentHistory() async {
    try {
      final payments = await _databaseService.getPaymentCollections();
      _paymentHistory.assignAll(payments);
    } catch (e) {
      debugPrint('Error loading payment history: $e');
      rethrow;
    }
  }

  Future<void> loadReminders() async {
    try {
      final reminderList = await _databaseService.getPaymentReminders();
      _reminders.assignAll(reminderList);
    } catch (e) {
      debugPrint('Error loading reminders: $e');
      rethrow;
    }
  }

  Future<void> loadCustomers() async {
    try {
      final users = await _databaseService.getUsers();
      _customers.assignAll(
        users.where((user) => user.role == UserRole.customer).toList(),
      );
    } catch (e) {
      debugPrint('Error loading customers: $e');
      rethrow;
    }
  }

  // Customer Account Analysis
  Map<String, dynamic> getCustomerAccountSummary(String customerId) {
    final customerBills =
        _customerBills.where((bill) => bill.customerId == customerId).toList();
    final customerPayments =
        _paymentHistory
            .where((payment) => payment.customerId == customerId)
            .toList();

    final totalOutstanding = customerBills.fold(
      0.0,
      (sum, bill) => sum + bill.pendingAmount,
    );
    final totalPaid = customerPayments.fold(
      0.0,
      (sum, payment) => sum + payment.amount,
    );
    final overdueBills = customerBills.where((bill) => bill.isOverdue).toList();
    final overdueAmount = overdueBills.fold(
      0.0,
      (sum, bill) => sum + bill.pendingAmount,
    );

    // Aging analysis
    final Map<String, double> agingAnalysis = {
      'Current': 0.0,
      '1-30 days': 0.0,
      '31-60 days': 0.0,
      '61-90 days': 0.0,
      '90+ days': 0.0,
    };

    for (final bill in customerBills.where((b) => b.isPending)) {
      agingAnalysis[bill.agingCategory] =
          (agingAnalysis[bill.agingCategory] ?? 0.0) + bill.pendingAmount;
    }

    // Payment behavior analysis
    final lastPaymentDate =
        customerPayments.isNotEmpty
            ? customerPayments
                .map((p) => p.collectionDate)
                .reduce((a, b) => a.isAfter(b) ? a : b)
            : null;

    final averagePaymentAmount =
        customerPayments.isNotEmpty
            ? customerPayments.fold(
                  0.0,
                  (sum, payment) => sum + payment.amount,
                ) /
                customerPayments.length
            : 0.0;

    return {
      'customerId': customerId,
      'totalOutstanding': totalOutstanding,
      'totalPaid': totalPaid,
      'overdueAmount': overdueAmount,
      'overdueBillsCount': overdueBills.length,
      'agingAnalysis': agingAnalysis,
      'lastPaymentDate': lastPaymentDate,
      'averagePaymentAmount': averagePaymentAmount,
      'totalBills': customerBills.length,
      'totalPayments': customerPayments.length,
      'isBlocked': _isCustomerBlocked(customerId),
      'shouldBlock': _shouldBlockCustomer(customerId),
    };
  }

  // Customer Blocking Logic
  bool _isCustomerBlocked(String customerId) {
    return _blockedCustomers.any((customer) => customer.id == customerId);
  }

  bool _shouldBlockCustomer(String customerId) {
    final summary = getCustomerAccountSummary(customerId);
    final overdueAmount = summary['overdueAmount'] as double;
    final overdueBillsCount = summary['overdueBillsCount'] as int;

    // Block if overdue amount exceeds threshold or has bills overdue beyond grace period
    if (overdueAmount >= _blockingThreshold.value) return true;

    final customerBills =
        _customerBills
            .where(
              (bill) =>
                  bill.customerId == customerId &&
                  bill.isOverdue &&
                  bill.daysPastDue > _paymentGracePeriod.value,
            )
            .toList();

    return customerBills.isNotEmpty;
  }

  void _updateBlockedCustomers() {
    final shouldBeBlocked = <User>[];

    for (final customer in _customers) {
      if (_shouldBlockCustomer(customer.id) &&
          !_isCustomerBlocked(customer.id)) {
        shouldBeBlocked.add(customer);
      }
    }

    _blockedCustomers.assignAll(shouldBeBlocked);
  }

  Future<bool> blockCustomer(String customerId, {String? reason}) async {
    try {
      final customer = _customers.firstWhere((c) => c.id == customerId);

      // TODO: Update customer status in database
      // For now, just add to blocked list
      if (!_isCustomerBlocked(customerId)) {
        _blockedCustomers.add(customer);
      }

      Get.snackbar(
        'Customer Blocked',
        'Customer ${customer.name} has been blocked from placing new orders',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.warning,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error blocking customer: $e');
      return false;
    }
  }

  Future<bool> unblockCustomer(String customerId) async {
    try {
      _blockedCustomers.removeWhere((customer) => customer.id == customerId);

      final customer = _customers.firstWhere((c) => c.id == customerId);
      Get.snackbar(
        'Customer Unblocked',
        'Customer ${customer.name} can now place orders',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error unblocking customer: $e');
      return false;
    }
  }

  // Payment Reminder System
  Future<bool> sendPaymentReminder(
    String customerId,
    ReminderType type, {
    String? customMessage,
  }) async {
    try {
      _isLoading.value = true;

      final customer = _customers.firstWhere((c) => c.id == customerId);
      final summary = getCustomerAccountSummary(customerId);
      final pendingAmount = summary['totalOutstanding'] as double;

      String message =
          customMessage ??
          _getDefaultReminderMessage(type, customer.name, pendingAmount);

      final reminder = PaymentReminder(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        customerId: customerId,
        customerName: customer.name,
        pendingAmount: pendingAmount,
        type: type,
        message: message,
        deliveryMethods: [BillDeliveryMethod.sms, BillDeliveryMethod.inApp],
        scheduledAt: DateTime.now(),
        sentAt: DateTime.now(),
        isSent: true,
        createdBy: 'admin', // TODO: Get current user
        createdAt: DateTime.now(),
      );

      await _databaseService.insertPaymentReminder(reminder);
      _reminders.insert(0, reminder);

      // TODO: Implement actual sending logic (SMS, email, etc.)

      Get.snackbar(
        'Reminder Sent',
        'Payment reminder sent to ${customer.name}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.textOnPrimary,
      );

      return true;
    } catch (e) {
      debugPrint('Error sending payment reminder: $e');
      Get.snackbar(
        'Error',
        'Failed to send payment reminder',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.textOnPrimary,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  String _getDefaultReminderMessage(
    ReminderType type,
    String customerName,
    double amount,
  ) {
    switch (type) {
      case ReminderType.gentle:
        return 'Dear $customerName, this is a gentle reminder that you have a pending payment of ₹${amount.toStringAsFixed(2)}. Please make the payment at your earliest convenience.';
      case ReminderType.firm:
        return 'Dear $customerName, your payment of ₹${amount.toStringAsFixed(2)} is overdue. Please settle this amount immediately to avoid service interruption.';
      case ReminderType.finalNotice:
        return 'FINAL NOTICE: Dear $customerName, your payment of ₹${amount.toStringAsFixed(2)} is significantly overdue. Please pay immediately or your account will be suspended.';
      case ReminderType.legal:
        return 'LEGAL NOTICE: Dear $customerName, despite multiple reminders, your payment of ₹${amount.toStringAsFixed(2)} remains unpaid. Legal action may be initiated if not settled within 7 days.';
    }
  }

  // Automated Reminder System
  Future<void> processAutomatedReminders() async {
    if (!_autoRemindersEnabled.value) return;

    try {
      final overdueCustomers =
          _customers.where((customer) {
            final summary = getCustomerAccountSummary(customer.id);
            return summary['overdueAmount'] as double > 0;
          }).toList();

      for (final customer in overdueCustomers) {
        final lastReminder = _reminders
            .where((r) => r.customerId == customer.id)
            .fold<PaymentReminder?>(null, (latest, current) {
              if (latest == null) return current;
              return current.createdAt.isAfter(latest.createdAt)
                  ? current
                  : latest;
            });

        final daysSinceLastReminder =
            lastReminder != null
                ? DateTime.now().difference(lastReminder.createdAt).inDays
                : _reminderFrequency.value + 1; // Force first reminder

        if (daysSinceLastReminder >= _reminderFrequency.value) {
          final summary = getCustomerAccountSummary(customer.id);
          final overdueAmount = summary['overdueAmount'] as double;

          ReminderType reminderType = ReminderType.gentle;
          if (overdueAmount > _blockingThreshold.value) {
            reminderType = ReminderType.finalNotice;
          } else if (daysSinceLastReminder > 14) {
            reminderType = ReminderType.firm;
          }

          await sendPaymentReminder(customer.id, reminderType);
        }
      }
    } catch (e) {
      debugPrint('Error processing automated reminders: $e');
    }
  }

  // Settings Management
  void updatePaymentGracePeriod(int days) {
    _paymentGracePeriod.value = days;
    _updateBlockedCustomers();
  }

  void updateBlockingThreshold(double amount) {
    _blockingThreshold.value = amount;
    _updateBlockedCustomers();
  }

  void updateAutoReminders(bool enabled) {
    _autoRemindersEnabled.value = enabled;
  }

  void updateReminderFrequency(int days) {
    _reminderFrequency.value = days;
  }

  // Filtering and Searching
  void setSearchQuery(String query) {
    _searchQuery.value = query;
  }

  void setCustomerFilter(String customerId) {
    _selectedCustomer.value = customerId;
  }

  void setAgingFilter(String aging) {
    _selectedAging.value = aging;
  }

  void setSorting(String sortField, bool ascending) {
    _sortBy.value = sortField;
    _sortAscending.value = ascending;
  }

  // Analytics and Reporting
  Map<String, dynamic> getOverallAccountsAnalytics() {
    final totalOutstanding = _customerBills.fold(
      0.0,
      (sum, bill) => sum + bill.pendingAmount,
    );
    final totalOverdue = _customerBills
        .where((bill) => bill.isOverdue)
        .fold(0.0, (sum, bill) => sum + bill.pendingAmount);

    final customersWithOutstanding =
        _customers.where((customer) {
          final summary = getCustomerAccountSummary(customer.id);
          return summary['totalOutstanding'] as double > 0;
        }).length;

    final customersOverdue =
        _customers.where((customer) {
          final summary = getCustomerAccountSummary(customer.id);
          return summary['overdueAmount'] as double > 0;
        }).length;

    // Aging analysis across all customers
    final Map<String, double> overallAging = {
      'Current': 0.0,
      '1-30 days': 0.0,
      '31-60 days': 0.0,
      '61-90 days': 0.0,
      '90+ days': 0.0,
    };

    for (final bill in _customerBills.where((b) => b.isPending)) {
      overallAging[bill.agingCategory] =
          (overallAging[bill.agingCategory] ?? 0.0) + bill.pendingAmount;
    }

    return {
      'totalOutstanding': totalOutstanding,
      'totalOverdue': totalOverdue,
      'customersWithOutstanding': customersWithOutstanding,
      'customersOverdue': customersOverdue,
      'blockedCustomersCount': _blockedCustomers.length,
      'overallAging': overallAging,
      'collectionEfficiency':
          totalOutstanding > 0
              ? ((totalOutstanding - totalOverdue) / totalOutstanding) * 100
              : 100,
      'averageOutstandingPerCustomer':
          customersWithOutstanding > 0
              ? totalOutstanding / customersWithOutstanding
              : 0,
    };
  }

  // Quick access methods
  List<User> getCustomersWithOutstanding() {
    return _customers.where((customer) {
      final summary = getCustomerAccountSummary(customer.id);
      return summary['totalOutstanding'] as double > 0;
    }).toList();
  }

  List<User> getOverdueCustomers() {
    return _customers.where((customer) {
      final summary = getCustomerAccountSummary(customer.id);
      return summary['overdueAmount'] as double > 0;
    }).toList();
  }

  List<User> getCustomersRequiringAttention() {
    return _customers
        .where((customer) => _shouldBlockCustomer(customer.id))
        .toList();
  }

  List<PaymentReminder> getRecentReminders({int days = 7}) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return _reminders
        .where((reminder) => reminder.createdAt.isAfter(cutoffDate))
        .toList();
  }

  List<PaymentCollection> getRecentPayments({int days = 7}) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return _paymentHistory
        .where((payment) => payment.collectionDate.isAfter(cutoffDate))
        .toList();
  }

  double getTotalOutstandingAmount() {
    return _customerBills.fold(0.0, (sum, bill) => sum + bill.pendingAmount);
  }

  double getTotalOverdueAmount() {
    return _customerBills
        .where((bill) => bill.isOverdue)
        .fold(0.0, (sum, bill) => sum + bill.pendingAmount);
  }

  int getOverdueCustomersCount() {
    return getOverdueCustomers().length;
  }

  // Bulk Operations
  Future<bool> sendBulkReminders(
    List<String> customerIds,
    ReminderType type,
  ) async {
    try {
      _isLoading.value = true;
      int successCount = 0;

      for (String customerId in customerIds) {
        final success = await sendPaymentReminder(customerId, type);
        if (success) successCount++;
      }

      Get.snackbar(
        'Bulk Reminders',
        'Sent reminders to $successCount of ${customerIds.length} customers',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor:
            successCount == customerIds.length
                ? AppColors.success
                : AppColors.warning,
        colorText: AppColors.textOnPrimary,
      );

      return successCount > 0;
    } catch (e) {
      debugPrint('Error sending bulk reminders: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> bulkBlockCustomers(
    List<String> customerIds, {
    String? reason,
  }) async {
    try {
      _isLoading.value = true;
      int successCount = 0;

      for (String customerId in customerIds) {
        final success = await blockCustomer(customerId, reason: reason);
        if (success) successCount++;
      }

      Get.snackbar(
        'Bulk Block',
        'Blocked $successCount of ${customerIds.length} customers',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor:
            successCount == customerIds.length
                ? AppColors.success
                : AppColors.warning,
        colorText: AppColors.textOnPrimary,
      );

      return successCount > 0;
    } catch (e) {
      debugPrint('Error blocking customers: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
}
