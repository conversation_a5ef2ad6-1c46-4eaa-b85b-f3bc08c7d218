import 'package:get/get.dart';
import '../../models/order_template.dart';

class OrderTemplateController extends GetxController {
  // Observables
  final RxList<OrderTemplate> _templates = <OrderTemplate>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final Rx<OrderTemplate?> _selectedTemplate = Rx<OrderTemplate?>(null);

  // Getters
  List<OrderTemplate> get templates => _templates;
  List<OrderTemplate> get activeTemplates =>
      _templates.where((t) => t.isActive).toList();
  OrderTemplate? get defaultTemplate =>
      _templates.firstWhereOrNull((t) => t.isDefault && t.isActive);
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  OrderTemplate? get selectedTemplate => _selectedTemplate.value;
  bool get hasTemplates => _templates.isNotEmpty;

  @override
  void onInit() {
    super.onInit();
    loadTemplates();
  }

  // Load all templates for current customer
  Future<void> loadTemplates({String? customerId}) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // In a real app, this would fetch from database
      // For now, we'll simulate with sample data
      await Future.delayed(const Duration(milliseconds: 500));

      if (customerId != null) {
        _templates.value = await _loadCustomerTemplates(customerId);
      }
    } catch (e) {
      _error.value = 'Failed to load order templates: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  // Create new template
  Future<bool> createTemplate(OrderTemplate template) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // If this is set as default, unset other defaults
      if (template.isDefault) {
        await _unsetOtherDefaults(template.customerId);
      }

      // In a real app, save to database
      await Future.delayed(const Duration(milliseconds: 300));

      _templates.add(template);
      _templates.refresh();

      Get.snackbar(
        'Success',
        'Order template "${template.templateName}" created successfully',
        snackPosition: SnackPosition.BOTTOM,
      );

      return true;
    } catch (e) {
      _error.value = 'Failed to create template: $e';
      Get.snackbar(
        'Error',
        'Failed to create order template',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Update existing template
  Future<bool> updateTemplate(OrderTemplate template) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // If this is set as default, unset other defaults
      if (template.isDefault) {
        await _unsetOtherDefaults(template.customerId);
      }

      final index = _templates.indexWhere((t) => t.id == template.id);
      if (index != -1) {
        _templates[index] = template.copyWith(updatedAt: DateTime.now());
        _templates.refresh();

        Get.snackbar(
          'Success',
          'Order template updated successfully',
          snackPosition: SnackPosition.BOTTOM,
        );

        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'Failed to update template: $e';
      Get.snackbar(
        'Error',
        'Failed to update order template',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Delete template
  Future<bool> deleteTemplate(String templateId) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      await Future.delayed(const Duration(milliseconds: 300));

      _templates.removeWhere((t) => t.id == templateId);
      _templates.refresh();

      Get.snackbar(
        'Success',
        'Order template deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
      );

      return true;
    } catch (e) {
      _error.value = 'Failed to delete template: $e';
      Get.snackbar(
        'Error',
        'Failed to delete order template',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Set template as default
  Future<void> setAsDefault(String templateId) async {
    try {
      final template = _templates.firstWhereOrNull((t) => t.id == templateId);
      if (template != null) {
        // Unset other defaults
        await _unsetOtherDefaults(template.customerId);

        // Set this as default
        await updateTemplate(template.copyWith(isDefault: true));
      }
    } catch (e) {
      _error.value = 'Failed to set default template: $e';
    }
  }

  // Toggle template active status
  Future<void> toggleTemplateStatus(String templateId) async {
    try {
      final template = _templates.firstWhereOrNull((t) => t.id == templateId);
      if (template != null) {
        await updateTemplate(template.copyWith(isActive: !template.isActive));
      }
    } catch (e) {
      _error.value = 'Failed to toggle template status: $e';
    }
  }

  // Select template for editing
  void selectTemplate(OrderTemplate? template) {
    _selectedTemplate.value = template;
  }

  // Clear selection
  void clearSelection() {
    _selectedTemplate.value = null;
  }

  // Get template by ID
  OrderTemplate? getTemplateById(String templateId) {
    return _templates.firstWhereOrNull((t) => t.id == templateId);
  }

  // Private helper methods
  Future<void> _unsetOtherDefaults(String customerId) async {
    for (int i = 0; i < _templates.length; i++) {
      if (_templates[i].customerId == customerId && _templates[i].isDefault) {
        _templates[i] = _templates[i].copyWith(isDefault: false);
      }
    }
  }

  Future<List<OrderTemplate>> _loadCustomerTemplates(String customerId) async {
    // Sample data - in real app, this would come from database
    return [
      OrderTemplate(
        id: 'template_001',
        customerId: customerId,
        templateName: 'Daily Essentials',
        description: 'My regular daily milk order',
        items: [
          OrderTemplateItem(
            productId: 'product_001',
            productName: 'Full Cream Milk',
            productCategory: 'fullCream',
            quantity: 1.0,
            unitPrice: 60.0,
          ),
          OrderTemplateItem(
            productId: 'product_002',
            productName: 'Toned Milk',
            productCategory: 'toned',
            quantity: 0.5,
            unitPrice: 50.0,
          ),
        ],
        deliveryFrequency: DeliveryFrequency.daily,
        timePreference: DeliveryTimePreference.morning,
        isDefault: true,
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      OrderTemplate(
        id: 'template_002',
        customerId: customerId,
        templateName: 'Weekend Special',
        description: 'Extra milk for weekends',
        items: [
          OrderTemplateItem(
            productId: 'product_001',
            productName: 'Full Cream Milk',
            productCategory: 'fullCream',
            quantity: 2.0,
            unitPrice: 60.0,
          ),
          OrderTemplateItem(
            productId: 'product_004',
            productName: 'Buffalo Milk',
            productCategory: 'buffalo',
            quantity: 1.0,
            unitPrice: 70.0,
          ),
        ],
        deliveryFrequency: DeliveryFrequency.weekly,
        timePreference: DeliveryTimePreference.morning,
        customDeliveryDays: [6, 0], // Saturday and Sunday
        isDefault: false,
        createdAt: DateTime.now().subtract(const Duration(days: 14)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];
  }
}
