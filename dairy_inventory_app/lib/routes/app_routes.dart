import 'package:get/get.dart';
import '../presentation/pages/splash/splash_page.dart';
import '../presentation/pages/auth/login_page.dart';
import '../presentation/pages/admin/admin_dashboard.dart';
import '../presentation/pages/customer/customer_dashboard.dart';
import '../presentation/pages/field_man/field_man_dashboard.dart';
import '../presentation/controllers/auth_controller.dart';
import '../presentation/controllers/order_controller.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String admin = '/admin';
  static const String customer = '/customer';
  static const String fieldMan = '/field-man';
  
  static List<GetPage> routes = [
    GetPage(
      name: splash,
      page: () => const SplashPage(),
      binding: BindingsBuilder(() {
        Get.put(AuthController());
      }),
    ),
    GetPage(
      name: login,
      page: () => const LoginPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => AuthController());
      }),
    ),
    GetPage(
      name: admin,
      page: () => const AdminDashboard(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => AuthController());
        Get.lazyPut(() => OrderController());
      }),
    ),
    GetPage(
      name: customer,
      page: () => const CustomerDashboard(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => AuthController());
        Get.lazyPut(() => OrderController());
      }),
    ),
    GetPage(
      name: fieldMan,
      page: () => const FieldManDashboard(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => AuthController());
        Get.lazyPut(() => OrderController());
      }),
    ),
  ];
}
