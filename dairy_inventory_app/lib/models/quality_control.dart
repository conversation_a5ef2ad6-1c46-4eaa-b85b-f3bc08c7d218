enum LeakageCause {
  manufacturingDefect,
  handlingDamage,
  transportationIssue,
  storageIssue,
  temperatureFluctuation,
  packagingDefect,
  unknown
}

enum ResponsibleParty {
  supplier,
  deliveryStaff,
  customer,
  warehouse,
  transportation,
  unknown
}

enum LeakageStatus {
  reported,
  investigating,
  resolved,
  disputed,
  closed
}

enum LeakageSeverity {
  minor,    // < 5% of batch
  moderate, // 5-15% of batch
  major,    // 15-30% of batch
  critical  // > 30% of batch
}

class LeakageIncident {
  final String id;
  final DateTime incidentDate;
  final String productId;
  final String productName;
  final String productCategory;
  final String? batchNumber;
  final DateTime? expiryDate;
  final double quantityAffected; // in liters
  final int packetsAffected;
  final double unitCost;
  final double totalLoss;
  final LeakageCause cause;
  final ResponsibleParty responsibleParty;
  final LeakageStatus status;
  final LeakageSeverity severity;
  final String description;
  final List<String> photoUrls;
  final String? supplierId;
  final String? supplierName;
  final String? fieldManId;
  final String? fieldManName;
  final String? customerId;
  final String? customerName;
  final String? orderId;
  final String reportedBy;
  final DateTime reportedAt;
  final String? investigatedBy;
  final DateTime? investigatedAt;
  final String? resolutionNotes;
  final DateTime? resolvedAt;
  final double? compensationAmount;
  final bool isCompensated;
  final String? preventiveActions;
  final DateTime createdAt;
  final DateTime updatedAt;

  LeakageIncident({
    required this.id,
    required this.incidentDate,
    required this.productId,
    required this.productName,
    required this.productCategory,
    this.batchNumber,
    this.expiryDate,
    required this.quantityAffected,
    required this.packetsAffected,
    required this.unitCost,
    required this.totalLoss,
    required this.cause,
    required this.responsibleParty,
    required this.status,
    required this.severity,
    required this.description,
    this.photoUrls = const [],
    this.supplierId,
    this.supplierName,
    this.fieldManId,
    this.fieldManName,
    this.customerId,
    this.customerName,
    this.orderId,
    required this.reportedBy,
    required this.reportedAt,
    this.investigatedBy,
    this.investigatedAt,
    this.resolutionNotes,
    this.resolvedAt,
    this.compensationAmount,
    this.isCompensated = false,
    this.preventiveActions,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  bool get isResolved => status == LeakageStatus.resolved || status == LeakageStatus.closed;
  bool get isPending => status == LeakageStatus.reported || status == LeakageStatus.investigating;
  bool get requiresInvestigation => status == LeakageStatus.reported;
  bool get hasPhotos => photoUrls.isNotEmpty;
  int get daysSinceReported => DateTime.now().difference(reportedAt).inDays;
  
  String get causeDisplayName {
    switch (cause) {
      case LeakageCause.manufacturingDefect:
        return 'Manufacturing Defect';
      case LeakageCause.handlingDamage:
        return 'Handling Damage';
      case LeakageCause.transportationIssue:
        return 'Transportation Issue';
      case LeakageCause.storageIssue:
        return 'Storage Issue';
      case LeakageCause.temperatureFluctuation:
        return 'Temperature Fluctuation';
      case LeakageCause.packagingDefect:
        return 'Packaging Defect';
      case LeakageCause.unknown:
        return 'Unknown';
    }
  }

  String get responsiblePartyDisplayName {
    switch (responsibleParty) {
      case ResponsibleParty.supplier:
        return 'Supplier';
      case ResponsibleParty.deliveryStaff:
        return 'Delivery Staff';
      case ResponsibleParty.customer:
        return 'Customer';
      case ResponsibleParty.warehouse:
        return 'Warehouse';
      case ResponsibleParty.transportation:
        return 'Transportation';
      case ResponsibleParty.unknown:
        return 'Unknown';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case LeakageStatus.reported:
        return 'Reported';
      case LeakageStatus.investigating:
        return 'Under Investigation';
      case LeakageStatus.resolved:
        return 'Resolved';
      case LeakageStatus.disputed:
        return 'Disputed';
      case LeakageStatus.closed:
        return 'Closed';
    }
  }

  String get severityDisplayName {
    switch (severity) {
      case LeakageSeverity.minor:
        return 'Minor';
      case LeakageSeverity.moderate:
        return 'Moderate';
      case LeakageSeverity.major:
        return 'Major';
      case LeakageSeverity.critical:
        return 'Critical';
    }
  }

  factory LeakageIncident.fromMap(Map<String, dynamic> map) {
    return LeakageIncident(
      id: map['id'],
      incidentDate: DateTime.parse(map['incident_date']),
      productId: map['product_id'],
      productName: map['product_name'],
      productCategory: map['product_category'],
      batchNumber: map['batch_number'],
      expiryDate: map['expiry_date'] != null ? DateTime.parse(map['expiry_date']) : null,
      quantityAffected: map['quantity_affected'].toDouble(),
      packetsAffected: map['packets_affected'],
      unitCost: map['unit_cost'].toDouble(),
      totalLoss: map['total_loss'].toDouble(),
      cause: LeakageCause.values[map['cause']],
      responsibleParty: ResponsibleParty.values[map['responsible_party']],
      status: LeakageStatus.values[map['status']],
      severity: LeakageSeverity.values[map['severity']],
      description: map['description'],
      photoUrls: map['photo_urls'] != null ? List<String>.from(map['photo_urls']) : [],
      supplierId: map['supplier_id'],
      supplierName: map['supplier_name'],
      fieldManId: map['field_man_id'],
      fieldManName: map['field_man_name'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      orderId: map['order_id'],
      reportedBy: map['reported_by'],
      reportedAt: DateTime.parse(map['reported_at']),
      investigatedBy: map['investigated_by'],
      investigatedAt: map['investigated_at'] != null ? DateTime.parse(map['investigated_at']) : null,
      resolutionNotes: map['resolution_notes'],
      resolvedAt: map['resolved_at'] != null ? DateTime.parse(map['resolved_at']) : null,
      compensationAmount: map['compensation_amount']?.toDouble(),
      isCompensated: map['is_compensated'] == 1,
      preventiveActions: map['preventive_actions'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  LeakageIncident copyWith({
    String? id,
    DateTime? incidentDate,
    String? productId,
    String? productName,
    String? productCategory,
    String? batchNumber,
    DateTime? expiryDate,
    double? quantityAffected,
    int? packetsAffected,
    double? unitCost,
    double? totalLoss,
    LeakageCause? cause,
    ResponsibleParty? responsibleParty,
    LeakageStatus? status,
    LeakageSeverity? severity,
    String? description,
    List<String>? photoUrls,
    String? supplierId,
    String? supplierName,
    String? fieldManId,
    String? fieldManName,
    String? customerId,
    String? customerName,
    String? orderId,
    String? reportedBy,
    DateTime? reportedAt,
    String? investigatedBy,
    DateTime? investigatedAt,
    String? resolutionNotes,
    DateTime? resolvedAt,
    double? compensationAmount,
    bool? isCompensated,
    String? preventiveActions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LeakageIncident(
      id: id ?? this.id,
      incidentDate: incidentDate ?? this.incidentDate,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCategory: productCategory ?? this.productCategory,
      batchNumber: batchNumber ?? this.batchNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      quantityAffected: quantityAffected ?? this.quantityAffected,
      packetsAffected: packetsAffected ?? this.packetsAffected,
      unitCost: unitCost ?? this.unitCost,
      totalLoss: totalLoss ?? this.totalLoss,
      cause: cause ?? this.cause,
      responsibleParty: responsibleParty ?? this.responsibleParty,
      status: status ?? this.status,
      severity: severity ?? this.severity,
      description: description ?? this.description,
      photoUrls: photoUrls ?? this.photoUrls,
      supplierId: supplierId ?? this.supplierId,
      supplierName: supplierName ?? this.supplierName,
      fieldManId: fieldManId ?? this.fieldManId,
      fieldManName: fieldManName ?? this.fieldManName,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      orderId: orderId ?? this.orderId,
      reportedBy: reportedBy ?? this.reportedBy,
      reportedAt: reportedAt ?? this.reportedAt,
      investigatedBy: investigatedBy ?? this.investigatedBy,
      investigatedAt: investigatedAt ?? this.investigatedAt,
      resolutionNotes: resolutionNotes ?? this.resolutionNotes,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      compensationAmount: compensationAmount ?? this.compensationAmount,
      isCompensated: isCompensated ?? this.isCompensated,
      preventiveActions: preventiveActions ?? this.preventiveActions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'incident_date': incidentDate.toIso8601String(),
      'product_id': productId,
      'product_name': productName,
      'product_category': productCategory,
      'batch_number': batchNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      'quantity_affected': quantityAffected,
      'packets_affected': packetsAffected,
      'unit_cost': unitCost,
      'total_loss': totalLoss,
      'cause': cause.index,
      'responsible_party': responsibleParty.index,
      'status': status.index,
      'severity': severity.index,
      'description': description,
      'photo_urls': photoUrls,
      'supplier_id': supplierId,
      'supplier_name': supplierName,
      'field_man_id': fieldManId,
      'field_man_name': fieldManName,
      'customer_id': customerId,
      'customer_name': customerName,
      'order_id': orderId,
      'reported_by': reportedBy,
      'reported_at': reportedAt.toIso8601String(),
      'investigated_by': investigatedBy,
      'investigated_at': investigatedAt?.toIso8601String(),
      'resolution_notes': resolutionNotes,
      'resolved_at': resolvedAt?.toIso8601String(),
      'compensation_amount': compensationAmount,
      'is_compensated': isCompensated ? 1 : 0,
      'preventive_actions': preventiveActions,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class SupplierQualityScorecard {
  final String supplierId;
  final String supplierName;
  final int totalIncidents;
  final double totalLossAmount;
  final int totalPacketsSupplied;
  final double defectRate; // percentage
  final Map<LeakageCause, int> incidentsByCause;
  final Map<LeakageSeverity, int> incidentsBySeverity;
  final double averageResolutionTime; // in days
  final int compensatedIncidents;
  final double compensationAmount;
  final DateTime fromDate;
  final DateTime toDate;
  final double qualityScore; // 0-100

  SupplierQualityScorecard({
    required this.supplierId,
    required this.supplierName,
    required this.totalIncidents,
    required this.totalLossAmount,
    required this.totalPacketsSupplied,
    required this.defectRate,
    required this.incidentsByCause,
    required this.incidentsBySeverity,
    required this.averageResolutionTime,
    required this.compensatedIncidents,
    required this.compensationAmount,
    required this.fromDate,
    required this.toDate,
    required this.qualityScore,
  });

  String get qualityGrade {
    if (qualityScore >= 90) return 'A+';
    if (qualityScore >= 80) return 'A';
    if (qualityScore >= 70) return 'B';
    if (qualityScore >= 60) return 'C';
    return 'D';
  }

  bool get isGoodSupplier => qualityScore >= 80;
  bool get requiresAttention => qualityScore < 60;
}
