enum PaymentMethod { 
  cash, 
  online, 
  cheque, 
  upi 
}

class Payment {
  final String id;
  final String orderId;
  final String customerId;
  final String customerName;
  final String? fieldManId;
  final String? fieldManName;
  final double amount;
  final PaymentMethod method;
  final String? transactionId;
  final String? notes;
  final DateTime paymentDate;
  final DateTime createdAt;

  Payment({
    required this.id,
    required this.orderId,
    required this.customerId,
    required this.customerName,
    this.fieldManId,
    this.fieldManName,
    required this.amount,
    required this.method,
    this.transactionId,
    this.notes,
    required this.paymentDate,
    required this.createdAt,
  });

  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      id: map['id'],
      orderId: map['order_id'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      fieldManId: map['field_man_id'],
      fieldManName: map['field_man_name'],
      amount: map['amount'].toDouble(),
      method: PaymentMethod.values[map['method']],
      transactionId: map['transaction_id'],
      notes: map['notes'],
      paymentDate: DateTime.parse(map['payment_date']),
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'order_id': orderId,
      'customer_id': customerId,
      'customer_name': customerName,
      'field_man_id': fieldManId,
      'field_man_name': fieldManName,
      'amount': amount,
      'method': method.index,
      'transaction_id': transactionId,
      'notes': notes,
      'payment_date': paymentDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  Payment copyWith({
    String? id,
    String? orderId,
    String? customerId,
    String? customerName,
    String? fieldManId,
    String? fieldManName,
    double? amount,
    PaymentMethod? method,
    String? transactionId,
    String? notes,
    DateTime? paymentDate,
    DateTime? createdAt,
  }) {
    return Payment(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      fieldManId: fieldManId ?? this.fieldManId,
      fieldManName: fieldManName ?? this.fieldManName,
      amount: amount ?? this.amount,
      method: method ?? this.method,
      transactionId: transactionId ?? this.transactionId,
      notes: notes ?? this.notes,
      paymentDate: paymentDate ?? this.paymentDate,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
