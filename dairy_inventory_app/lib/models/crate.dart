enum CrateCondition {
  good,
  damaged,
  needsReplacement,
  lost,
  retired
}

enum CrateType {
  plastic,
  metal,
  wooden,
  glass
}

enum CrateSize {
  small,   // 6 bottles
  medium,  // 12 bottles
  large,   // 24 bottles
  extraLarge // 48 bottles
}

enum CrateMovementType {
  incoming,
  outgoing,
  returned,
  damaged,
  lost,
  maintenance,
  disposed
}

class CrateMovement {
  final String id;
  final String crateId;
  final CrateMovementType type;
  final int quantity;
  final String? orderId;
  final String? customerId;
  final String? fieldManId;
  final String? notes;
  final DateTime movementDate;
  final String recordedBy;
  final DateTime createdAt;

  CrateMovement({
    required this.id,
    required this.crateId,
    required this.type,
    required this.quantity,
    this.orderId,
    this.customerId,
    this.fieldManId,
    this.notes,
    required this.movementDate,
    required this.recordedBy,
    required this.createdAt,
  });

  factory CrateMovement.fromMap(Map<String, dynamic> map) {
    return CrateMovement(
      id: map['id'],
      crateId: map['crate_id'],
      type: CrateMovementType.values[map['type']],
      quantity: map['quantity'],
      orderId: map['order_id'],
      customerId: map['customer_id'],
      fieldManId: map['field_man_id'],
      notes: map['notes'],
      movementDate: DateTime.parse(map['movement_date']),
      recordedBy: map['recorded_by'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'crate_id': crateId,
      'type': type.index,
      'quantity': quantity,
      'order_id': orderId,
      'customer_id': customerId,
      'field_man_id': fieldManId,
      'notes': notes,
      'movement_date': movementDate.toIso8601String(),
      'recorded_by': recordedBy,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class Crate {
  final String id;
  final String name;
  final CrateType type;
  final CrateSize size;
  final CrateCondition condition;
  final int capacity; // number of bottles
  final double purchasePrice;
  final DateTime purchaseDate;
  final String? supplier;
  final String? serialNumber;
  final int currentStock;
  final int minimumStock;
  final int maximumStock;
  final String? location;
  final List<CrateMovement> movementHistory;
  final DateTime? lastMaintenanceDate;
  final DateTime? nextMaintenanceDate;
  final String? notes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Crate({
    required this.id,
    required this.name,
    required this.type,
    required this.size,
    required this.condition,
    required this.capacity,
    required this.purchasePrice,
    required this.purchaseDate,
    this.supplier,
    this.serialNumber,
    required this.currentStock,
    required this.minimumStock,
    required this.maximumStock,
    this.location,
    this.movementHistory = const [],
    this.lastMaintenanceDate,
    this.nextMaintenanceDate,
    this.notes,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  bool get isLowStock => currentStock <= minimumStock;
  bool get isOverStock => currentStock >= maximumStock;
  bool get needsMaintenance => nextMaintenanceDate != null && 
                              nextMaintenanceDate!.isBefore(DateTime.now());
  bool get isInGoodCondition => condition == CrateCondition.good;
  bool get needsReplacement => condition == CrateCondition.needsReplacement || 
                              condition == CrateCondition.damaged;
  
  double get stockPercentage => maximumStock > 0 ? (currentStock / maximumStock) * 100 : 0;
  
  int get availableStock => condition == CrateCondition.good ? currentStock : 0;
  
  String get conditionDisplayName {
    switch (condition) {
      case CrateCondition.good:
        return 'Good';
      case CrateCondition.damaged:
        return 'Damaged';
      case CrateCondition.needsReplacement:
        return 'Needs Replacement';
      case CrateCondition.lost:
        return 'Lost';
      case CrateCondition.retired:
        return 'Retired';
    }
  }
  
  String get typeDisplayName {
    switch (type) {
      case CrateType.plastic:
        return 'Plastic';
      case CrateType.metal:
        return 'Metal';
      case CrateType.wooden:
        return 'Wooden';
      case CrateType.glass:
        return 'Glass';
    }
  }
  
  String get sizeDisplayName {
    switch (size) {
      case CrateSize.small:
        return 'Small (6 bottles)';
      case CrateSize.medium:
        return 'Medium (12 bottles)';
      case CrateSize.large:
        return 'Large (24 bottles)';
      case CrateSize.extraLarge:
        return 'Extra Large (48 bottles)';
    }
  }

  // Business logic methods
  bool canBeUsed() {
    return isActive && 
           condition == CrateCondition.good && 
           currentStock > 0;
  }
  
  bool requiresAttention() {
    return isLowStock || needsMaintenance || needsReplacement;
  }
  
  int getDaysUntilMaintenance() {
    if (nextMaintenanceDate == null) return -1;
    return nextMaintenanceDate!.difference(DateTime.now()).inDays;
  }

  factory Crate.fromMap(Map<String, dynamic> map) {
    return Crate(
      id: map['id'],
      name: map['name'],
      type: CrateType.values[map['type']],
      size: CrateSize.values[map['size']],
      condition: CrateCondition.values[map['condition']],
      capacity: map['capacity'],
      purchasePrice: map['purchase_price'].toDouble(),
      purchaseDate: DateTime.parse(map['purchase_date']),
      supplier: map['supplier'],
      serialNumber: map['serial_number'],
      currentStock: map['current_stock'],
      minimumStock: map['minimum_stock'],
      maximumStock: map['maximum_stock'],
      location: map['location'],
      movementHistory: map['movement_history'] != null
          ? (map['movement_history'] as List)
              .map((movement) => CrateMovement.fromMap(movement))
              .toList()
          : [],
      lastMaintenanceDate: map['last_maintenance_date'] != null
          ? DateTime.parse(map['last_maintenance_date'])
          : null,
      nextMaintenanceDate: map['next_maintenance_date'] != null
          ? DateTime.parse(map['next_maintenance_date'])
          : null,
      notes: map['notes'],
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Crate copyWith({
    String? id,
    String? name,
    CrateType? type,
    CrateSize? size,
    CrateCondition? condition,
    int? capacity,
    double? purchasePrice,
    DateTime? purchaseDate,
    String? supplier,
    String? serialNumber,
    int? currentStock,
    int? minimumStock,
    int? maximumStock,
    String? location,
    List<CrateMovement>? movementHistory,
    DateTime? lastMaintenanceDate,
    DateTime? nextMaintenanceDate,
    String? notes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Crate(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      size: size ?? this.size,
      condition: condition ?? this.condition,
      capacity: capacity ?? this.capacity,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      supplier: supplier ?? this.supplier,
      serialNumber: serialNumber ?? this.serialNumber,
      currentStock: currentStock ?? this.currentStock,
      minimumStock: minimumStock ?? this.minimumStock,
      maximumStock: maximumStock ?? this.maximumStock,
      location: location ?? this.location,
      movementHistory: movementHistory ?? this.movementHistory,
      lastMaintenanceDate: lastMaintenanceDate ?? this.lastMaintenanceDate,
      nextMaintenanceDate: nextMaintenanceDate ?? this.nextMaintenanceDate,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.index,
      'size': size.index,
      'condition': condition.index,
      'capacity': capacity,
      'purchase_price': purchasePrice,
      'purchase_date': purchaseDate.toIso8601String(),
      'supplier': supplier,
      'serial_number': serialNumber,
      'current_stock': currentStock,
      'minimum_stock': minimumStock,
      'maximum_stock': maximumStock,
      'location': location,
      'movement_history': movementHistory.map((movement) => movement.toMap()).toList(),
      'last_maintenance_date': lastMaintenanceDate?.toIso8601String(),
      'next_maintenance_date': nextMaintenanceDate?.toIso8601String(),
      'notes': notes,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
