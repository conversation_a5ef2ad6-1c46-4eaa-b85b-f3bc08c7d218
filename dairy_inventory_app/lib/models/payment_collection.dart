import 'billing.dart';

enum ChequeStatus {
  pending,
  cleared,
  bounced,
  cancelled
}

enum CollectionStatus {
  pending,
  completed,
  verified,
  disputed
}

class PaymentCollection {
  final String id;
  final String customerId;
  final String customerName;
  final String? billId;
  final double amount;
  final PaymentMethod paymentMethod;
  final PaymentStatus status;
  final DateTime collectionDate;
  final String collectedBy;
  final String? fieldManId;
  final String? fieldManName;
  final String? referenceNumber;
  final String? chequeNumber;
  final String? bankName;
  final DateTime? chequeDate;
  final ChequeStatus? chequeStatus;
  final DateTime? clearanceDate;
  final String? notes;
  final List<String> attachments; // Photo URLs for receipts/cheques
  final bool isVerified;
  final String? verifiedBy;
  final DateTime? verifiedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentCollection({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.billId,
    required this.amount,
    required this.paymentMethod,
    required this.status,
    required this.collectionDate,
    required this.collectedBy,
    this.fieldManId,
    this.fieldManName,
    this.referenceNumber,
    this.chequeNumber,
    this.bankName,
    this.chequeDate,
    this.chequeStatus,
    this.clearanceDate,
    this.notes,
    this.attachments = const [],
    this.isVerified = false,
    this.verifiedBy,
    this.verifiedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  bool get isChequePayment => paymentMethod == PaymentMethod.cheque;
  bool get isCashPayment => paymentMethod == PaymentMethod.cash;
  bool get isOnlinePayment => paymentMethod == PaymentMethod.onlineTransfer ||
                             paymentMethod == PaymentMethod.upi ||
                             paymentMethod == PaymentMethod.netBanking;
  bool get requiresVerification => isChequePayment || amount > 10000; // High value cash
  bool get isPending => status == PaymentStatus.pending;
  bool get isCompleted => status == PaymentStatus.completed;
  
  String get paymentMethodDisplayName {
    switch (paymentMethod) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.onlineTransfer:
        return 'Online Transfer';
      case PaymentMethod.cheque:
        return 'Cheque';
      case PaymentMethod.upi:
        return 'UPI';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.netBanking:
        return 'Net Banking';
    }
  }

  factory PaymentCollection.fromMap(Map<String, dynamic> map) {
    return PaymentCollection(
      id: map['id'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      billId: map['bill_id'],
      amount: map['amount'].toDouble(),
      paymentMethod: PaymentMethod.values[map['payment_method']],
      status: PaymentStatus.values[map['status']],
      collectionDate: DateTime.parse(map['collection_date']),
      collectedBy: map['collected_by'],
      fieldManId: map['field_man_id'],
      fieldManName: map['field_man_name'],
      referenceNumber: map['reference_number'],
      chequeNumber: map['cheque_number'],
      bankName: map['bank_name'],
      chequeDate: map['cheque_date'] != null ? DateTime.parse(map['cheque_date']) : null,
      chequeStatus: map['cheque_status'] != null ? ChequeStatus.values[map['cheque_status']] : null,
      clearanceDate: map['clearance_date'] != null ? DateTime.parse(map['clearance_date']) : null,
      notes: map['notes'],
      attachments: map['attachments'] != null ? List<String>.from(map['attachments']) : [],
      isVerified: map['is_verified'] == 1,
      verifiedBy: map['verified_by'],
      verifiedAt: map['verified_at'] != null ? DateTime.parse(map['verified_at']) : null,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  PaymentCollection copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? billId,
    double? amount,
    PaymentMethod? paymentMethod,
    PaymentStatus? status,
    DateTime? collectionDate,
    String? collectedBy,
    String? fieldManId,
    String? fieldManName,
    String? referenceNumber,
    String? chequeNumber,
    String? bankName,
    DateTime? chequeDate,
    ChequeStatus? chequeStatus,
    DateTime? clearanceDate,
    String? notes,
    List<String>? attachments,
    bool? isVerified,
    String? verifiedBy,
    DateTime? verifiedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentCollection(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      billId: billId ?? this.billId,
      amount: amount ?? this.amount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      collectionDate: collectionDate ?? this.collectionDate,
      collectedBy: collectedBy ?? this.collectedBy,
      fieldManId: fieldManId ?? this.fieldManId,
      fieldManName: fieldManName ?? this.fieldManName,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      chequeNumber: chequeNumber ?? this.chequeNumber,
      bankName: bankName ?? this.bankName,
      chequeDate: chequeDate ?? this.chequeDate,
      chequeStatus: chequeStatus ?? this.chequeStatus,
      clearanceDate: clearanceDate ?? this.clearanceDate,
      notes: notes ?? this.notes,
      attachments: attachments ?? this.attachments,
      isVerified: isVerified ?? this.isVerified,
      verifiedBy: verifiedBy ?? this.verifiedBy,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'bill_id': billId,
      'amount': amount,
      'payment_method': paymentMethod.index,
      'status': status.index,
      'collection_date': collectionDate.toIso8601String(),
      'collected_by': collectedBy,
      'field_man_id': fieldManId,
      'field_man_name': fieldManName,
      'reference_number': referenceNumber,
      'cheque_number': chequeNumber,
      'bank_name': bankName,
      'cheque_date': chequeDate?.toIso8601String(),
      'cheque_status': chequeStatus?.index,
      'clearance_date': clearanceDate?.toIso8601String(),
      'notes': notes,
      'attachments': attachments,
      'is_verified': isVerified ? 1 : 0,
      'verified_by': verifiedBy,
      'verified_at': verifiedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class PaymentReminder {
  final String id;
  final String customerId;
  final String customerName;
  final String? billId;
  final double pendingAmount;
  final ReminderType type;
  final String message;
  final List<BillDeliveryMethod> deliveryMethods;
  final DateTime scheduledAt;
  final DateTime? sentAt;
  final bool isSent;
  final String? response;
  final String createdBy;
  final DateTime createdAt;

  PaymentReminder({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.billId,
    required this.pendingAmount,
    required this.type,
    required this.message,
    required this.deliveryMethods,
    required this.scheduledAt,
    this.sentAt,
    this.isSent = false,
    this.response,
    required this.createdBy,
    required this.createdAt,
  });

  String get typeDisplayName {
    switch (type) {
      case ReminderType.gentle:
        return 'Gentle Reminder';
      case ReminderType.firm:
        return 'Firm Reminder';
      case ReminderType.final:
        return 'Final Notice';
      case ReminderType.legal:
        return 'Legal Notice';
    }
  }

  factory PaymentReminder.fromMap(Map<String, dynamic> map) {
    return PaymentReminder(
      id: map['id'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      billId: map['bill_id'],
      pendingAmount: map['pending_amount'].toDouble(),
      type: ReminderType.values[map['type']],
      message: map['message'],
      deliveryMethods: (map['delivery_methods'] as List)
          .map((method) => BillDeliveryMethod.values[method])
          .toList(),
      scheduledAt: DateTime.parse(map['scheduled_at']),
      sentAt: map['sent_at'] != null ? DateTime.parse(map['sent_at']) : null,
      isSent: map['is_sent'] == 1,
      response: map['response'],
      createdBy: map['created_by'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'bill_id': billId,
      'pending_amount': pendingAmount,
      'type': type.index,
      'message': message,
      'delivery_methods': deliveryMethods.map((method) => method.index).toList(),
      'scheduled_at': scheduledAt.toIso8601String(),
      'sent_at': sentAt?.toIso8601String(),
      'is_sent': isSent ? 1 : 0,
      'response': response,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class DailyCollectionSummary {
  final DateTime date;
  final double totalCashCollection;
  final double totalOnlineCollection;
  final double totalChequeCollection;
  final int totalTransactions;
  final Map<String, double> collectionByFieldStaff;
  final Map<PaymentMethod, double> collectionByMethod;
  final Map<PaymentMethod, int> transactionsByMethod;
  final double targetAmount;
  final double variance;
  final List<PaymentCollection> pendingVerifications;

  DailyCollectionSummary({
    required this.date,
    required this.totalCashCollection,
    required this.totalOnlineCollection,
    required this.totalChequeCollection,
    required this.totalTransactions,
    required this.collectionByFieldStaff,
    required this.collectionByMethod,
    required this.transactionsByMethod,
    required this.targetAmount,
    required this.variance,
    required this.pendingVerifications,
  });

  double get totalCollection => totalCashCollection + totalOnlineCollection + totalChequeCollection;
  double get achievementPercentage => targetAmount > 0 ? (totalCollection / targetAmount) * 100 : 0;
  bool get isTargetAchieved => totalCollection >= targetAmount;
}
