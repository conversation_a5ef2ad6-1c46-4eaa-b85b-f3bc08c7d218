enum BillStatus { draft, generated, sent, paid, overdue, cancelled }

enum BillDeliveryMethod { email, sms, inApp, print, whatsapp }

enum PaymentMethod { cash, onlineTransfer, cheque, upi, card, netBanking }

enum PaymentStatus {
  pending,
  completed,
  failed,
  cancelled,
  refunded,
  partiallyPaid,
}

enum ReminderType { gentle, firm, finalNotice, legal }

class BillItem {
  final String productId;
  final String productName;
  final String productCategory;
  final double quantity;
  final double unitPrice;
  final double totalAmount;
  final double? discountAmount;
  final DateTime orderDate;
  final String orderId;

  BillItem({
    required this.productId,
    required this.productName,
    required this.productCategory,
    required this.quantity,
    required this.unitPrice,
    required this.totalAmount,
    this.discountAmount,
    required this.orderDate,
    required this.orderId,
  });

  double get finalAmount => totalAmount - (discountAmount ?? 0.0);

  factory BillItem.fromMap(Map<String, dynamic> map) {
    return BillItem(
      productId: map['product_id'],
      productName: map['product_name'],
      productCategory: map['product_category'],
      quantity: map['quantity'].toDouble(),
      unitPrice: map['unit_price'].toDouble(),
      totalAmount: map['total_amount'].toDouble(),
      discountAmount: map['discount_amount']?.toDouble(),
      orderDate: DateTime.parse(map['order_date']),
      orderId: map['order_id'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'product_id': productId,
      'product_name': productName,
      'product_category': productCategory,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_amount': totalAmount,
      'discount_amount': discountAmount,
      'order_date': orderDate.toIso8601String(),
      'order_id': orderId,
    };
  }
}

class Bill {
  final String id;
  final String customerId;
  final String customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? customerAddress;
  final DateTime billDate;
  final DateTime fromDate;
  final DateTime toDate;
  final List<BillItem> items;
  final double subtotal;
  final double? discountAmount;
  final double? taxAmount;
  final double totalAmount;
  final double paidAmount;
  final double pendingAmount;
  final BillStatus status;
  final List<BillDeliveryMethod> deliveryMethods;
  final DateTime? sentAt;
  final DateTime? dueDate;
  final String? notes;
  final String generatedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  Bill({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.customerAddress,
    required this.billDate,
    required this.fromDate,
    required this.toDate,
    required this.items,
    required this.subtotal,
    this.discountAmount,
    this.taxAmount,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.pendingAmount,
    required this.status,
    this.deliveryMethods = const [],
    this.sentAt,
    this.dueDate,
    this.notes,
    required this.generatedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  double get finalAmount =>
      totalAmount - (discountAmount ?? 0.0) + (taxAmount ?? 0.0);
  bool get isOverdue =>
      dueDate != null &&
      dueDate!.isBefore(DateTime.now()) &&
      status != BillStatus.paid;
  bool get isPaid => status == BillStatus.paid;
  bool get isPending => pendingAmount > 0;
  int get daysPastDue =>
      isOverdue ? DateTime.now().difference(dueDate!).inDays : 0;

  String get agingCategory {
    if (!isOverdue) return 'Current';
    final days = daysPastDue;
    if (days <= 30) return '1-30 days';
    if (days <= 60) return '31-60 days';
    if (days <= 90) return '61-90 days';
    return '90+ days';
  }

  factory Bill.fromMap(Map<String, dynamic> map) {
    return Bill(
      id: map['id'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      customerEmail: map['customer_email'],
      customerPhone: map['customer_phone'],
      customerAddress: map['customer_address'],
      billDate: DateTime.parse(map['bill_date']),
      fromDate: DateTime.parse(map['from_date']),
      toDate: DateTime.parse(map['to_date']),
      items:
          (map['items'] as List).map((item) => BillItem.fromMap(item)).toList(),
      subtotal: map['subtotal'].toDouble(),
      discountAmount: map['discount_amount']?.toDouble(),
      taxAmount: map['tax_amount']?.toDouble(),
      totalAmount: map['total_amount'].toDouble(),
      paidAmount: map['paid_amount']?.toDouble() ?? 0.0,
      pendingAmount: map['pending_amount'].toDouble(),
      status: BillStatus.values[map['status']],
      deliveryMethods:
          map['delivery_methods'] != null
              ? (map['delivery_methods'] as List)
                  .map((method) => BillDeliveryMethod.values[method])
                  .toList()
              : [],
      sentAt: map['sent_at'] != null ? DateTime.parse(map['sent_at']) : null,
      dueDate: map['due_date'] != null ? DateTime.parse(map['due_date']) : null,
      notes: map['notes'],
      generatedBy: map['generated_by'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Bill copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    String? customerAddress,
    DateTime? billDate,
    DateTime? fromDate,
    DateTime? toDate,
    List<BillItem>? items,
    double? subtotal,
    double? discountAmount,
    double? taxAmount,
    double? totalAmount,
    double? paidAmount,
    double? pendingAmount,
    BillStatus? status,
    List<BillDeliveryMethod>? deliveryMethods,
    DateTime? sentAt,
    DateTime? dueDate,
    String? notes,
    String? generatedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Bill(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      customerPhone: customerPhone ?? this.customerPhone,
      customerAddress: customerAddress ?? this.customerAddress,
      billDate: billDate ?? this.billDate,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      discountAmount: discountAmount ?? this.discountAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      pendingAmount: pendingAmount ?? this.pendingAmount,
      status: status ?? this.status,
      deliveryMethods: deliveryMethods ?? this.deliveryMethods,
      sentAt: sentAt ?? this.sentAt,
      dueDate: dueDate ?? this.dueDate,
      notes: notes ?? this.notes,
      generatedBy: generatedBy ?? this.generatedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_email': customerEmail,
      'customer_phone': customerPhone,
      'customer_address': customerAddress,
      'bill_date': billDate.toIso8601String(),
      'from_date': fromDate.toIso8601String(),
      'to_date': toDate.toIso8601String(),
      'items': items.map((item) => item.toMap()).toList(),
      'subtotal': subtotal,
      'discount_amount': discountAmount,
      'tax_amount': taxAmount,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'pending_amount': pendingAmount,
      'status': status.index,
      'delivery_methods':
          deliveryMethods.map((method) => method.index).toList(),
      'sent_at': sentAt?.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'notes': notes,
      'generated_by': generatedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
