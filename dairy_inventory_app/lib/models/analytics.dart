enum ReportType {
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
  custom
}

enum ReportCategory {
  orders,
  crates,
  revenue,
  customers,
  fieldStaff,
  products,
  inventory
}

class DateRange {
  final DateTime startDate;
  final DateTime endDate;

  DateRange({
    required this.startDate,
    required this.endDate,
  });

  int get daysDifference => endDate.difference(startDate).inDays;
  
  bool contains(DateTime date) {
    return date.isAfter(startDate.subtract(const Duration(days: 1))) &&
           date.isBefore(endDate.add(const Duration(days: 1)));
  }

  factory DateRange.today() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return DateRange(
      startDate: today,
      endDate: today.add(const Duration(days: 1)),
    );
  }

  factory DateRange.thisWeek() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return DateRange(
      startDate: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
      endDate: DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day),
    );
  }

  factory DateRange.thisMonth() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    return DateRange(
      startDate: startOfMonth,
      endDate: endOfMonth,
    );
  }

  factory DateRange.thisYear() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);
    return DateRange(
      startDate: startOfYear,
      endDate: endOfYear,
    );
  }
}

class OrderAnalytics {
  final int totalOrders;
  final int pendingOrders;
  final int confirmedOrders;
  final int deliveredOrders;
  final int cancelledOrders;
  final double totalRevenue;
  final double averageOrderValue;
  final double totalPendingAmount;
  final Map<String, int> ordersByCategory;
  final Map<String, double> revenueByCategory;
  final Map<String, int> ordersByTimeSlot;
  final List<Map<String, dynamic>> topCustomers;
  final DateRange dateRange;

  OrderAnalytics({
    required this.totalOrders,
    required this.pendingOrders,
    required this.confirmedOrders,
    required this.deliveredOrders,
    required this.cancelledOrders,
    required this.totalRevenue,
    required this.averageOrderValue,
    required this.totalPendingAmount,
    required this.ordersByCategory,
    required this.revenueByCategory,
    required this.ordersByTimeSlot,
    required this.topCustomers,
    required this.dateRange,
  });

  double get completionRate => totalOrders > 0 ? (deliveredOrders / totalOrders) * 100 : 0;
  double get cancellationRate => totalOrders > 0 ? (cancelledOrders / totalOrders) * 100 : 0;
  double get pendingRate => totalOrders > 0 ? (pendingOrders / totalOrders) * 100 : 0;
}

class CrateAnalytics {
  final int totalCrates;
  final int availableCrates;
  final int cratesInUse;
  final int damagedCrates;
  final int lostCrates;
  final Map<String, int> cratesByCondition;
  final Map<String, int> cratesByType;
  final Map<String, int> cratesBySize;
  final List<Map<String, dynamic>> lowStockCrates;
  final List<Map<String, dynamic>> maintenanceRequired;
  final double totalCrateValue;
  final int cratesDeliveredToday;
  final int cratesReturnedToday;
  final DateRange dateRange;

  CrateAnalytics({
    required this.totalCrates,
    required this.availableCrates,
    required this.cratesInUse,
    required this.damagedCrates,
    required this.lostCrates,
    required this.cratesByCondition,
    required this.cratesByType,
    required this.cratesBySize,
    required this.lowStockCrates,
    required this.maintenanceRequired,
    required this.totalCrateValue,
    required this.cratesDeliveredToday,
    required this.cratesReturnedToday,
    required this.dateRange,
  });

  double get utilizationRate => totalCrates > 0 ? (cratesInUse / totalCrates) * 100 : 0;
  double get damageRate => totalCrates > 0 ? (damagedCrates / totalCrates) * 100 : 0;
  double get availabilityRate => totalCrates > 0 ? (availableCrates / totalCrates) * 100 : 0;
  int get cratesBalance => cratesDeliveredToday - cratesReturnedToday;
}

class RevenueAnalytics {
  final double totalRevenue;
  final double todayRevenue;
  final double weekRevenue;
  final double monthRevenue;
  final double yearRevenue;
  final double averageDailyRevenue;
  final double averageWeeklyRevenue;
  final double averageMonthlyRevenue;
  final Map<String, double> revenueByProduct;
  final Map<String, double> revenueByCustomer;
  final Map<String, double> revenueByTimeSlot;
  final List<Map<String, dynamic>> revenueGrowth;
  final DateRange dateRange;

  RevenueAnalytics({
    required this.totalRevenue,
    required this.todayRevenue,
    required this.weekRevenue,
    required this.monthRevenue,
    required this.yearRevenue,
    required this.averageDailyRevenue,
    required this.averageWeeklyRevenue,
    required this.averageMonthlyRevenue,
    required this.revenueByProduct,
    required this.revenueByCustomer,
    required this.revenueByTimeSlot,
    required this.revenueGrowth,
    required this.dateRange,
  });

  double get growthRate {
    if (revenueGrowth.length < 2) return 0;
    final current = revenueGrowth.last['revenue'] as double;
    final previous = revenueGrowth[revenueGrowth.length - 2]['revenue'] as double;
    return previous > 0 ? ((current - previous) / previous) * 100 : 0;
  }
}

class CustomerAnalytics {
  final int totalCustomers;
  final int activeCustomers;
  final int newCustomersThisMonth;
  final int recurringCustomers;
  final double averageOrdersPerCustomer;
  final double averageRevenuePerCustomer;
  final Map<String, int> customersByLocation;
  final List<Map<String, dynamic>> topCustomersByRevenue;
  final List<Map<String, dynamic>> topCustomersByOrders;
  final List<Map<String, dynamic>> customerRetention;
  final DateRange dateRange;

  CustomerAnalytics({
    required this.totalCustomers,
    required this.activeCustomers,
    required this.newCustomersThisMonth,
    required this.recurringCustomers,
    required this.averageOrdersPerCustomer,
    required this.averageRevenuePerCustomer,
    required this.customersByLocation,
    required this.topCustomersByRevenue,
    required this.topCustomersByOrders,
    required this.customerRetention,
    required this.dateRange,
  });

  double get activeCustomerRate => totalCustomers > 0 ? (activeCustomers / totalCustomers) * 100 : 0;
  double get recurringCustomerRate => totalCustomers > 0 ? (recurringCustomers / totalCustomers) * 100 : 0;
}

class FieldStaffAnalytics {
  final int totalFieldStaff;
  final int activeFieldStaff;
  final Map<String, int> ordersByFieldStaff;
  final Map<String, double> revenueByFieldStaff;
  final Map<String, double> deliveryTimeByFieldStaff;
  final List<Map<String, dynamic>> topPerformers;
  final List<Map<String, dynamic>> workloadDistribution;
  final DateRange dateRange;

  FieldStaffAnalytics({
    required this.totalFieldStaff,
    required this.activeFieldStaff,
    required this.ordersByFieldStaff,
    required this.revenueByFieldStaff,
    required this.deliveryTimeByFieldStaff,
    required this.topPerformers,
    required this.workloadDistribution,
    required this.dateRange,
  });

  double get averageOrdersPerStaff => activeFieldStaff > 0 
      ? ordersByFieldStaff.values.reduce((a, b) => a + b) / activeFieldStaff 
      : 0;
}

class DashboardSummary {
  final OrderAnalytics orderAnalytics;
  final CrateAnalytics crateAnalytics;
  final RevenueAnalytics revenueAnalytics;
  final CustomerAnalytics customerAnalytics;
  final FieldStaffAnalytics fieldStaffAnalytics;
  final List<Map<String, dynamic>> alerts;
  final List<Map<String, dynamic>> notifications;
  final DateTime generatedAt;

  DashboardSummary({
    required this.orderAnalytics,
    required this.crateAnalytics,
    required this.revenueAnalytics,
    required this.customerAnalytics,
    required this.fieldStaffAnalytics,
    required this.alerts,
    required this.notifications,
    required this.generatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'order_analytics': {
        'total_orders': orderAnalytics.totalOrders,
        'pending_orders': orderAnalytics.pendingOrders,
        'delivered_orders': orderAnalytics.deliveredOrders,
        'total_revenue': orderAnalytics.totalRevenue,
        'completion_rate': orderAnalytics.completionRate,
      },
      'crate_analytics': {
        'total_crates': crateAnalytics.totalCrates,
        'available_crates': crateAnalytics.availableCrates,
        'utilization_rate': crateAnalytics.utilizationRate,
        'damage_rate': crateAnalytics.damageRate,
      },
      'revenue_analytics': {
        'total_revenue': revenueAnalytics.totalRevenue,
        'today_revenue': revenueAnalytics.todayRevenue,
        'growth_rate': revenueAnalytics.growthRate,
      },
      'customer_analytics': {
        'total_customers': customerAnalytics.totalCustomers,
        'active_customers': customerAnalytics.activeCustomers,
        'new_customers': customerAnalytics.newCustomersThisMonth,
      },
      'alerts': alerts,
      'notifications': notifications,
      'generated_at': generatedAt.toIso8601String(),
    };
  }
}
