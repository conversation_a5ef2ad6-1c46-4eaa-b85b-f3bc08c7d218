enum MilkCategory { 
  fullCream, 
  toned, 
  doubleToned, 
  skimmed, 
  buffalo, 
  organic 
}

class MilkProduct {
  final String id;
  final String name;
  final MilkCategory category;
  final double pricePerLiter;
  final double fatContent;
  final String description;
  final bool isActive;
  final DateTime createdAt;

  MilkProduct({
    required this.id,
    required this.name,
    required this.category,
    required this.pricePerLiter,
    required this.fatContent,
    required this.description,
    this.isActive = true,
    required this.createdAt,
  });

  factory MilkProduct.fromMap(Map<String, dynamic> map) {
    return MilkProduct(
      id: map['id'],
      name: map['name'],
      category: MilkCategory.values[map['category']],
      pricePerLiter: map['price_per_liter'].toDouble(),
      fatContent: map['fat_content'].toDouble(),
      description: map['description'],
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category.index,
      'price_per_liter': pricePerLiter,
      'fat_content': fatContent,
      'description': description,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  MilkProduct copyWith({
    String? id,
    String? name,
    MilkCategory? category,
    double? pricePerLiter,
    double? fatContent,
    String? description,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return MilkProduct(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      pricePerLiter: pricePerLiter ?? this.pricePerLiter,
      fatContent: fatContent ?? this.fatContent,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
