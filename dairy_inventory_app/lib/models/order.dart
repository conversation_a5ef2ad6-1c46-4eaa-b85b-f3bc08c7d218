enum OrderStatus {
  pending,
  confirmed,
  inProgress,
  delivered,
  cancelled,
  onHold,
  returned,
}

enum PaymentStatus { pending, paid, partiallyPaid, overdue }

class OrderItem {
  final String productId;
  final String productName;
  final String productCategory;
  final double quantity; // in liters
  final double unitPrice;
  final double totalPrice;

  OrderItem({
    required this.productId,
    required this.productName,
    required this.productCategory,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      productId: map['product_id'],
      productName: map['product_name'],
      productCategory: map['product_category'] ?? 'Unknown',
      quantity: map['quantity'].toDouble(),
      unitPrice: map['unit_price'].toDouble(),
      totalPrice: map['total_price'].toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'product_id': productId,
      'product_name': productName,
      'product_category': productCategory,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
    };
  }
}

class Order {
  final String id;
  final String customerId;
  final String customerName;
  final String? fieldManId;
  final String? fieldManName;
  final List<OrderItem> items;
  final double totalAmount;
  final double paidAmount;
  final double pendingAmount;
  final OrderStatus status;
  final PaymentStatus paymentStatus;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final String? notes;
  final bool isRecurring;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.fieldManId,
    this.fieldManName,
    required this.items,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.pendingAmount,
    required this.status,
    required this.paymentStatus,
    required this.orderDate,
    this.deliveryDate,
    this.notes,
    this.isRecurring = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      fieldManId: map['field_man_id'],
      fieldManName: map['field_man_name'],
      items:
          (map['items'] as List)
              .map((item) => OrderItem.fromMap(item))
              .toList(),
      totalAmount: map['total_amount'].toDouble(),
      paidAmount: map['paid_amount'].toDouble(),
      pendingAmount: map['pending_amount'].toDouble(),
      status: OrderStatus.values[map['status']],
      paymentStatus: PaymentStatus.values[map['payment_status']],
      orderDate: DateTime.parse(map['order_date']),
      deliveryDate:
          map['delivery_date'] != null
              ? DateTime.parse(map['delivery_date'])
              : null,
      notes: map['notes'],
      isRecurring: map['is_recurring'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Order copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? fieldManId,
    String? fieldManName,
    List<OrderItem>? items,
    double? totalAmount,
    double? paidAmount,
    double? pendingAmount,
    OrderStatus? status,
    PaymentStatus? paymentStatus,
    DateTime? orderDate,
    DateTime? deliveryDate,
    String? notes,
    bool? isRecurring,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      fieldManId: fieldManId ?? this.fieldManId,
      fieldManName: fieldManName ?? this.fieldManName,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      pendingAmount: pendingAmount ?? this.pendingAmount,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      orderDate: orderDate ?? this.orderDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      notes: notes ?? this.notes,
      isRecurring: isRecurring ?? this.isRecurring,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'field_man_id': fieldManId,
      'field_man_name': fieldManName,
      'items': items.map((item) => item.toMap()).toList(),
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'pending_amount': pendingAmount,
      'status': status.index,
      'payment_status': paymentStatus.index,
      'order_date': orderDate.toIso8601String(),
      'delivery_date': deliveryDate?.toIso8601String(),
      'notes': notes,
      'is_recurring': isRecurring ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
