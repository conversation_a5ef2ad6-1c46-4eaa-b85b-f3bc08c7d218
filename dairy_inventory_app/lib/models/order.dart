enum OrderStatus {
  pending,
  confirmed,
  inProgress,
  delivered,
  cancelled,
  onHold,
  returned,
}

enum PaymentStatus { pending, paid, partiallyPaid, overdue, refunded }

class OrderAssignment {
  final String assignmentId;
  final String fieldManId;
  final String fieldManName;
  final DateTime assignedAt;
  final String? assignedBy;
  final String? notes;
  final bool isActive;

  OrderAssignment({
    required this.assignmentId,
    required this.fieldManId,
    required this.fieldManName,
    required this.assignedAt,
    this.assignedBy,
    this.notes,
    this.isActive = true,
  });

  factory OrderAssignment.fromMap(Map<String, dynamic> map) {
    return OrderAssignment(
      assignmentId: map['assignment_id'],
      fieldManId: map['field_man_id'],
      fieldManName: map['field_man_name'],
      assignedAt: DateTime.parse(map['assigned_at']),
      assignedBy: map['assigned_by'],
      notes: map['notes'],
      isActive: map['is_active'] == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'assignment_id': assignmentId,
      'field_man_id': fieldManId,
      'field_man_name': fieldManName,
      'assigned_at': assignedAt.toIso8601String(),
      'assigned_by': assignedBy,
      'notes': notes,
      'is_active': isActive ? 1 : 0,
    };
  }
}

class OrderItem {
  final String productId;
  final String productName;
  final String productCategory;
  final double quantity; // in liters
  final double unitPrice;
  final double totalPrice;
  final double? discountAmount;

  OrderItem({
    required this.productId,
    required this.productName,
    required this.productCategory,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.discountAmount,
  });

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      productId: map['product_id'],
      productName: map['product_name'],
      productCategory: map['product_category'] ?? 'Unknown',
      quantity: map['quantity'].toDouble(),
      unitPrice: map['unit_price'].toDouble(),
      totalPrice: map['total_price'].toDouble(),
      discountAmount: map['discount_amount']?.toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'product_id': productId,
      'product_name': productName,
      'product_category': productCategory,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'discount_amount': discountAmount,
    };
  }
}

class Order {
  final String id;
  final String customerId;
  final String customerName;
  final String? customerPhone;
  final String? customerAddress;
  final String? fieldManId;
  final String? fieldManName;
  final List<OrderItem> items;
  final double totalAmount;
  final double paidAmount;
  final double pendingAmount;
  final OrderStatus status;
  final PaymentStatus paymentStatus;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final String? notes;
  final String? adminNotes;
  final bool isRecurring;
  final DateTime? actualDeliveryDate;
  final DateTime? cancelledAt;
  final List<OrderAssignment> assignmentHistory;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.customerPhone,
    this.customerAddress,
    this.fieldManId,
    this.fieldManName,
    required this.items,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.pendingAmount,
    required this.status,
    required this.paymentStatus,
    required this.orderDate,
    this.deliveryDate,
    this.notes,
    this.adminNotes,
    this.isRecurring = false,
    this.actualDeliveryDate,
    this.cancelledAt,
    this.assignmentHistory = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  bool get isAssigned => fieldManId != null;
  bool get isDelivered => status == OrderStatus.delivered;
  bool get isCancelled => status == OrderStatus.cancelled;
  bool get isPending => status == OrderStatus.pending;
  bool get isConfirmed => status == OrderStatus.confirmed;
  bool get isInProgress =>
      status == OrderStatus.inProgress || status == OrderStatus.confirmed;
  bool get isOverdue =>
      deliveryDate != null &&
      deliveryDate!.isBefore(DateTime.now()) &&
      status != OrderStatus.delivered;

  // Business logic methods
  bool canBeCancelled() {
    return status != OrderStatus.delivered &&
        status != OrderStatus.cancelled &&
        status != OrderStatus.returned;
  }

  bool canBeEdited() {
    return status == OrderStatus.pending || status == OrderStatus.confirmed;
  }

  bool canBeAssigned() {
    return (status == OrderStatus.pending || status == OrderStatus.confirmed) &&
        !isAssigned;
  }

  String getStatusDisplayName() {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProgress:
        return 'In Progress';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.onHold:
        return 'On Hold';
      case OrderStatus.returned:
        return 'Returned';
    }
  }

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      customerPhone: map['customer_phone'],
      customerAddress: map['customer_address'],
      fieldManId: map['field_man_id'],
      fieldManName: map['field_man_name'],
      items:
          (map['items'] as List)
              .map((item) => OrderItem.fromMap(item))
              .toList(),
      totalAmount: map['total_amount'].toDouble(),
      paidAmount: map['paid_amount']?.toDouble() ?? 0.0,
      pendingAmount: map['pending_amount'].toDouble(),
      status: OrderStatus.values[map['status']],
      paymentStatus: PaymentStatus.values[map['payment_status']],
      orderDate: DateTime.parse(map['order_date']),
      deliveryDate:
          map['delivery_date'] != null
              ? DateTime.parse(map['delivery_date'])
              : null,
      notes: map['notes'],
      adminNotes: map['admin_notes'],
      isRecurring: map['is_recurring'] == 1,
      actualDeliveryDate:
          map['actual_delivery_date'] != null
              ? DateTime.parse(map['actual_delivery_date'])
              : null,
      cancelledAt:
          map['cancelled_at'] != null
              ? DateTime.parse(map['cancelled_at'])
              : null,
      assignmentHistory:
          map['assignment_history'] != null
              ? (map['assignment_history'] as List)
                  .map((assignment) => OrderAssignment.fromMap(assignment))
                  .toList()
              : [],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Order copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? customerAddress,
    String? fieldManId,
    String? fieldManName,
    List<OrderItem>? items,
    double? totalAmount,
    double? paidAmount,
    double? pendingAmount,
    OrderStatus? status,
    PaymentStatus? paymentStatus,
    DateTime? orderDate,
    DateTime? deliveryDate,
    String? notes,
    String? adminNotes,
    bool? isRecurring,
    DateTime? actualDeliveryDate,
    DateTime? cancelledAt,
    List<OrderAssignment>? assignmentHistory,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerAddress: customerAddress ?? this.customerAddress,
      fieldManId: fieldManId ?? this.fieldManId,
      fieldManName: fieldManName ?? this.fieldManName,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      pendingAmount: pendingAmount ?? this.pendingAmount,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      orderDate: orderDate ?? this.orderDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      notes: notes ?? this.notes,
      adminNotes: adminNotes ?? this.adminNotes,
      isRecurring: isRecurring ?? this.isRecurring,
      actualDeliveryDate: actualDeliveryDate ?? this.actualDeliveryDate,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      assignmentHistory: assignmentHistory ?? this.assignmentHistory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'field_man_id': fieldManId,
      'field_man_name': fieldManName,
      'items': items.map((item) => item.toMap()).toList(),
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'pending_amount': pendingAmount,
      'status': status.index,
      'payment_status': paymentStatus.index,
      'order_date': orderDate.toIso8601String(),
      'delivery_date': deliveryDate?.toIso8601String(),
      'notes': notes,
      'is_recurring': isRecurring ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
