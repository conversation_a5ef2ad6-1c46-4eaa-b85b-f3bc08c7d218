enum OrderStatus {
  pending,
  confirmed,
  inProgress,
  delivered,
  cancelled,
  onHold,
  returned,
}

enum PaymentStatus { pending, paid, partiallyPaid, overdue, refunded }

enum OrderPriority { low, normal, high, urgent }

enum DeliveryTimeSlot {
  morning, // 6:00 AM - 10:00 AM
  afternoon, // 10:00 AM - 2:00 PM
  evening, // 2:00 PM - 6:00 PM
  night, // 6:00 PM - 10:00 PM
}

enum OrderType { oneTime, recurring, subscription }

class OrderItem {
  final String productId;
  final String productName;
  final String productCategory; // whole milk, skim milk, etc.
  final double quantity; // in liters
  final double unitPrice;
  final double totalPrice;
  final double? discountAmount;
  final String? notes;
  final bool isAvailable;
  final DateTime? expiryDate;

  OrderItem({
    required this.productId,
    required this.productName,
    required this.productCategory,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.discountAmount,
    this.notes,
    this.isAvailable = true,
    this.expiryDate,
  });

  double get finalPrice => totalPrice - (discountAmount ?? 0.0);

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      productId: map['product_id'],
      productName: map['product_name'],
      productCategory: map['product_category'] ?? 'Unknown',
      quantity: map['quantity'].toDouble(),
      unitPrice: map['unit_price'].toDouble(),
      totalPrice: map['total_price'].toDouble(),
      discountAmount: map['discount_amount']?.toDouble(),
      notes: map['notes'],
      isAvailable: map['is_available'] == 1,
      expiryDate:
          map['expiry_date'] != null
              ? DateTime.parse(map['expiry_date'])
              : null,
    );
  }

  OrderItem copyWith({
    String? productId,
    String? productName,
    String? productCategory,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    double? discountAmount,
    String? notes,
    bool? isAvailable,
    DateTime? expiryDate,
  }) {
    return OrderItem(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCategory: productCategory ?? this.productCategory,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      discountAmount: discountAmount ?? this.discountAmount,
      notes: notes ?? this.notes,
      isAvailable: isAvailable ?? this.isAvailable,
      expiryDate: expiryDate ?? this.expiryDate,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'product_id': productId,
      'product_name': productName,
      'product_category': productCategory,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'discount_amount': discountAmount,
      'notes': notes,
      'is_available': isAvailable ? 1 : 0,
      'expiry_date': expiryDate?.toIso8601String(),
    };
  }
}

class OrderAssignment {
  final String assignmentId;
  final String fieldManId;
  final String fieldManName;
  final DateTime assignedAt;
  final String? assignedBy;
  final String? notes;
  final bool isActive;

  OrderAssignment({
    required this.assignmentId,
    required this.fieldManId,
    required this.fieldManName,
    required this.assignedAt,
    this.assignedBy,
    this.notes,
    this.isActive = true,
  });

  factory OrderAssignment.fromMap(Map<String, dynamic> map) {
    return OrderAssignment(
      assignmentId: map['assignment_id'],
      fieldManId: map['field_man_id'],
      fieldManName: map['field_man_name'],
      assignedAt: DateTime.parse(map['assigned_at']),
      assignedBy: map['assigned_by'],
      notes: map['notes'],
      isActive: map['is_active'] == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'assignment_id': assignmentId,
      'field_man_id': fieldManId,
      'field_man_name': fieldManName,
      'assigned_at': assignedAt.toIso8601String(),
      'assigned_by': assignedBy,
      'notes': notes,
      'is_active': isActive ? 1 : 0,
    };
  }
}

class Order {
  final String id;
  final String customerId;
  final String customerName;
  final String? customerPhone;
  final String? customerAddress;
  final String? fieldManId;
  final String? fieldManName;
  final List<OrderItem> items;
  final double totalAmount;
  final double paidAmount;
  final double pendingAmount;
  final double? discountAmount;
  final double? taxAmount;
  final OrderStatus status;
  final PaymentStatus paymentStatus;
  final OrderPriority priority;
  final OrderType orderType;
  final DeliveryTimeSlot? deliveryTimeSlot;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final DateTime? actualDeliveryDate;
  final String? notes;
  final String? adminNotes;
  final bool isRecurring;
  final int? recurringDays;
  final List<OrderAssignment> assignmentHistory;
  final String? cancellationReason;
  final DateTime? cancelledAt;
  final String? cancelledBy;
  final int cratesRequired;
  final int cratesDelivered;
  final int cratesReturned;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.customerPhone,
    this.customerAddress,
    this.fieldManId,
    this.fieldManName,
    required this.items,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.pendingAmount,
    this.discountAmount,
    this.taxAmount,
    required this.status,
    required this.paymentStatus,
    this.priority = OrderPriority.normal,
    this.orderType = OrderType.oneTime,
    this.deliveryTimeSlot,
    required this.orderDate,
    this.deliveryDate,
    this.actualDeliveryDate,
    this.notes,
    this.adminNotes,
    this.isRecurring = false,
    this.recurringDays,
    this.assignmentHistory = const [],
    this.cancellationReason,
    this.cancelledAt,
    this.cancelledBy,
    this.cratesRequired = 0,
    this.cratesDelivered = 0,
    this.cratesReturned = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  double get finalAmount =>
      totalAmount - (discountAmount ?? 0.0) + (taxAmount ?? 0.0);
  bool get isOverdue =>
      deliveryDate != null &&
      deliveryDate!.isBefore(DateTime.now()) &&
      status != OrderStatus.delivered;
  bool get isAssigned => fieldManId != null && fieldManId!.isNotEmpty;
  bool get isCancelled => status == OrderStatus.cancelled;
  bool get isCompleted => status == OrderStatus.delivered;
  bool get isPending => status == OrderStatus.pending;
  bool get isInProgress =>
      status == OrderStatus.inProgress || status == OrderStatus.confirmed;
  int get cratesPending => cratesRequired - cratesDelivered;
  int get cratesBalance => cratesDelivered - cratesReturned;

  // Business logic methods
  bool canBeEdited() {
    return status == OrderStatus.pending || status == OrderStatus.confirmed;
  }

  bool canBeCancelled() {
    return status != OrderStatus.delivered &&
        status != OrderStatus.cancelled &&
        status != OrderStatus.returned;
  }

  bool canBeAssigned() {
    return (status == OrderStatus.confirmed || status == OrderStatus.pending) &&
        !isAssigned;
  }

  bool canBeReassigned() {
    return isAssigned && !isCompleted && !isCancelled;
  }

  String getStatusDisplayName() {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProgress:
        return 'In Progress';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.onHold:
        return 'On Hold';
      case OrderStatus.returned:
        return 'Returned';
    }
  }

  String getPriorityDisplayName() {
    switch (priority) {
      case OrderPriority.low:
        return 'Low';
      case OrderPriority.normal:
        return 'Normal';
      case OrderPriority.high:
        return 'High';
      case OrderPriority.urgent:
        return 'Urgent';
    }
  }

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      fieldManId: map['field_man_id'],
      fieldManName: map['field_man_name'],
      items:
          (map['items'] as List)
              .map((item) => OrderItem.fromMap(item))
              .toList(),
      totalAmount: map['total_amount'].toDouble(),
      paidAmount: map['paid_amount'].toDouble(),
      pendingAmount: map['pending_amount'].toDouble(),
      status: OrderStatus.values[map['status']],
      paymentStatus: PaymentStatus.values[map['payment_status']],
      orderDate: DateTime.parse(map['order_date']),
      deliveryDate:
          map['delivery_date'] != null
              ? DateTime.parse(map['delivery_date'])
              : null,
      notes: map['notes'],
      isRecurring: map['is_recurring'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Order copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? fieldManId,
    String? fieldManName,
    List<OrderItem>? items,
    double? totalAmount,
    double? paidAmount,
    double? pendingAmount,
    OrderStatus? status,
    PaymentStatus? paymentStatus,
    DateTime? orderDate,
    DateTime? deliveryDate,
    String? notes,
    bool? isRecurring,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      fieldManId: fieldManId ?? this.fieldManId,
      fieldManName: fieldManName ?? this.fieldManName,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      pendingAmount: pendingAmount ?? this.pendingAmount,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      orderDate: orderDate ?? this.orderDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      notes: notes ?? this.notes,
      isRecurring: isRecurring ?? this.isRecurring,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'field_man_id': fieldManId,
      'field_man_name': fieldManName,
      'items': items.map((item) => item.toMap()).toList(),
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'pending_amount': pendingAmount,
      'status': status.index,
      'payment_status': paymentStatus.index,
      'order_date': orderDate.toIso8601String(),
      'delivery_date': deliveryDate?.toIso8601String(),
      'notes': notes,
      'is_recurring': isRecurring ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
