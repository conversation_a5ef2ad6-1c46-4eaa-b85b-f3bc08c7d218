import 'package:flutter/foundation.dart';
import '../models/payment.dart';
import '../services/database_service.dart';

class PaymentProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  List<Payment> _payments = [];
  bool _isLoading = false;
  
  List<Payment> get payments => _payments;
  bool get isLoading => _isLoading;

  Future<void> loadPayments({String? customerId, String? orderId}) async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _payments = await _databaseService.getPayments(
        customerId: customerId,
        orderId: orderId,
      );
    } catch (e) {
      debugPrint('Error loading payments: $e');
    }
    
    _isLoading = false;
    notifyListeners();
  }

  Future<bool> addPayment(Payment payment) async {
    try {
      await _databaseService.insertPayment(payment);
      _payments.insert(0, payment);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding payment: $e');
      return false;
    }
  }

  List<Payment> getPaymentsByCustomer(String customerId) {
    return _payments.where((payment) => payment.customerId == customerId).toList();
  }

  List<Payment> getPaymentsByOrder(String orderId) {
    return _payments.where((payment) => payment.orderId == orderId).toList();
  }

  List<Payment> getTodaysPayments() {
    final today = DateTime.now();
    return _payments.where((payment) {
      return payment.paymentDate.year == today.year &&
             payment.paymentDate.month == today.month &&
             payment.paymentDate.day == today.day;
    }).toList();
  }

  List<Payment> getPaymentsByDateRange(DateTime startDate, DateTime endDate) {
    return _payments.where((payment) {
      return payment.paymentDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
             payment.paymentDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  double getTotalPaymentsAmount() {
    return _payments.fold(0.0, (sum, payment) => sum + payment.amount);
  }

  double getTodaysCollection() {
    final todaysPayments = getTodaysPayments();
    return todaysPayments.fold(0.0, (sum, payment) => sum + payment.amount);
  }

  Map<PaymentMethod, double> getPaymentsByMethod() {
    final Map<PaymentMethod, double> methodTotals = {};
    
    for (final payment in _payments) {
      methodTotals[payment.method] = (methodTotals[payment.method] ?? 0) + payment.amount;
    }
    
    return methodTotals;
  }

  List<Payment> getCashPayments() {
    return _payments.where((payment) => payment.method == PaymentMethod.cash).toList();
  }

  double getCashCollectionTotal() {
    final cashPayments = getCashPayments();
    return cashPayments.fold(0.0, (sum, payment) => sum + payment.amount);
  }
}
