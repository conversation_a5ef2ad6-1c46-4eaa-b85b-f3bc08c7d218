import 'package:flutter/foundation.dart';
import '../models/order.dart';
import '../services/database_service.dart';

class OrderProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  List<Order> _orders = [];
  bool _isLoading = false;
  
  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;

  Future<void> loadOrders({String? customerId, String? fieldManId}) async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _orders = await _databaseService.getOrders(
        customerId: customerId,
        fieldManId: fieldManId,
      );
    } catch (e) {
      debugPrint('Error loading orders: $e');
    }
    
    _isLoading = false;
    notifyListeners();
  }

  Future<bool> createOrder(Order order) async {
    try {
      await _databaseService.insertOrder(order);
      _orders.insert(0, order);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error creating order: $e');
      return false;
    }
  }

  Future<bool> updateOrderStatus(String orderId, OrderStatus status) async {
    try {
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        final updatedOrder = Order(
          id: _orders[orderIndex].id,
          customerId: _orders[orderIndex].customerId,
          customerName: _orders[orderIndex].customerName,
          fieldManId: _orders[orderIndex].fieldManId,
          fieldManName: _orders[orderIndex].fieldManName,
          items: _orders[orderIndex].items,
          totalAmount: _orders[orderIndex].totalAmount,
          paidAmount: _orders[orderIndex].paidAmount,
          pendingAmount: _orders[orderIndex].pendingAmount,
          status: status,
          paymentStatus: _orders[orderIndex].paymentStatus,
          orderDate: _orders[orderIndex].orderDate,
          deliveryDate: status == OrderStatus.delivered ? DateTime.now() : _orders[orderIndex].deliveryDate,
          notes: _orders[orderIndex].notes,
          isRecurring: _orders[orderIndex].isRecurring,
          createdAt: _orders[orderIndex].createdAt,
          updatedAt: DateTime.now(),
        );
        
        _orders[orderIndex] = updatedOrder;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating order status: $e');
      return false;
    }
  }

  Future<bool> assignFieldMan(String orderId, String fieldManId, String fieldManName) async {
    try {
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        final updatedOrder = Order(
          id: _orders[orderIndex].id,
          customerId: _orders[orderIndex].customerId,
          customerName: _orders[orderIndex].customerName,
          fieldManId: fieldManId,
          fieldManName: fieldManName,
          items: _orders[orderIndex].items,
          totalAmount: _orders[orderIndex].totalAmount,
          paidAmount: _orders[orderIndex].paidAmount,
          pendingAmount: _orders[orderIndex].pendingAmount,
          status: _orders[orderIndex].status,
          paymentStatus: _orders[orderIndex].paymentStatus,
          orderDate: _orders[orderIndex].orderDate,
          deliveryDate: _orders[orderIndex].deliveryDate,
          notes: _orders[orderIndex].notes,
          isRecurring: _orders[orderIndex].isRecurring,
          createdAt: _orders[orderIndex].createdAt,
          updatedAt: DateTime.now(),
        );
        
        _orders[orderIndex] = updatedOrder;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error assigning field man: $e');
      return false;
    }
  }

  List<Order> getOrdersByStatus(OrderStatus status) {
    return _orders.where((order) => order.status == status).toList();
  }

  List<Order> getTodaysOrders() {
    final today = DateTime.now();
    return _orders.where((order) {
      return order.orderDate.year == today.year &&
             order.orderDate.month == today.month &&
             order.orderDate.day == today.day;
    }).toList();
  }

  double getTotalRevenue() {
    return _orders.fold(0.0, (sum, order) => sum + order.totalAmount);
  }

  double getPendingAmount() {
    return _orders.fold(0.0, (sum, order) => sum + order.pendingAmount);
  }
}
