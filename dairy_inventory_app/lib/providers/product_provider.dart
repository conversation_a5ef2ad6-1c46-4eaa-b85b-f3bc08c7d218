import 'package:flutter/foundation.dart';
import '../models/milk_product.dart';
import '../services/database_service.dart';

class ProductProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  List<MilkProduct> _products = [];
  bool _isLoading = false;
  
  List<MilkProduct> get products => _products;
  bool get isLoading => _isLoading;

  Future<void> loadProducts() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _products = await _databaseService.getMilkProducts();
    } catch (e) {
      debugPrint('Error loading products: $e');
    }
    
    _isLoading = false;
    notifyListeners();
  }

  Future<bool> addProduct(MilkProduct product) async {
    try {
      await _databaseService.insertMilkProduct(product);
      _products.add(product);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding product: $e');
      return false;
    }
  }

  Future<bool> updateProduct(MilkProduct product) async {
    try {
      final index = _products.indexWhere((p) => p.id == product.id);
      if (index != -1) {
        _products[index] = product;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating product: $e');
      return false;
    }
  }

  Future<bool> deleteProduct(String productId) async {
    try {
      final index = _products.indexWhere((p) => p.id == productId);
      if (index != -1) {
        final updatedProduct = _products[index].copyWith(isActive: false);
        _products[index] = updatedProduct;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting product: $e');
      return false;
    }
  }

  List<MilkProduct> getProductsByCategory(MilkCategory category) {
    return _products.where((product) => product.category == category).toList();
  }

  MilkProduct? getProductById(String id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  List<MilkProduct> searchProducts(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _products.where((product) {
      return product.name.toLowerCase().contains(lowercaseQuery) ||
             product.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
}
