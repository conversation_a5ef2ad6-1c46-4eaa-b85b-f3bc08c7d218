import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  
  User? get currentUser => _authService.currentUser;
  bool get isLoggedIn => _authService.isLoggedIn;
  bool get isLoading => _isLoading;

  bool _isLoading = false;

  Future<void> checkAuthStatus() async {
    _isLoading = true;
    notifyListeners();
    
    await _authService.checkAuthStatus();
    
    _isLoading = false;
    notifyListeners();
  }

  Future<bool> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();
    
    final success = await _authService.login(email, password);
    
    _isLoading = false;
    notifyListeners();
    
    return success;
  }

  Future<bool> register({
    required String name,
    required String email,
    required String phone,
    required UserRole role,
    String? address,
  }) async {
    _isLoading = true;
    notifyListeners();
    
    final success = await _authService.register(
      name: name,
      email: email,
      phone: phone,
      role: role,
      address: address,
    );
    
    _isLoading = false;
    notifyListeners();
    
    return success;
  }

  Future<void> logout() async {
    await _authService.logout();
    notifyListeners();
  }

  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? address,
  }) async {
    final success = await _authService.updateProfile(
      name: name,
      phone: phone,
      address: address,
    );
    
    if (success) {
      notifyListeners();
    }
    
    return success;
  }

  bool hasRole(UserRole role) => _authService.hasRole(role);
  bool isAdmin() => _authService.isAdmin();
  bool isCustomer() => _authService.isCustomer();
  bool isFieldMan() => _authService.isFieldMan();
}
