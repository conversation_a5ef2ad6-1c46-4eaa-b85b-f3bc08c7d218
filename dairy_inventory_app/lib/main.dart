import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'core/themes/app_theme.dart';
import 'routes/app_routes.dart';
import 'services/data_init_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize sample data
  await DataInitService().initializeSampleData();

  runApp(const DairyFreshApp());
}

class DairyFreshApp extends StatelessWidget {
  const DairyFreshApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Dairy Fresh',
      theme: AppTheme.lightTheme,
      initialRoute: AppRoutes.splash,
      getPages: AppRoutes.routes,
      debugShowCheckedModeBanner: false,
      defaultTransition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 300),
    );
  }
}
