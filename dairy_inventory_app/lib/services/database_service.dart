import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/milk_product.dart';
import '../models/order.dart';
import '../models/payment.dart';
import '../models/notification.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  // Storage keys
  static const String _usersKey = 'users';
  static const String _productsKey = 'products';
  static const String _ordersKey = 'orders';
  static const String _paymentsKey = 'payments';
  static const String _notificationsKey = 'notifications';
  static const String _initializedKey = 'database_initialized';

  // Initialize database with sample data
  Future<void> initializeDatabase() async {
    final prefs = await _prefs;
    final isInitialized = prefs.getBool(_initializedKey) ?? false;

    if (!isInitialized) {
      await _createSampleData();
      await prefs.setBool(_initializedKey, true);
    }
  }

  Future<SharedPreferences> get _prefs async =>
      await SharedPreferences.getInstance();

  // Helper methods for JSON serialization
  Future<List<T>> _getList<T>(
    String key,
    T Function(Map<String, dynamic>) fromMap,
  ) async {
    final prefs = await _prefs;
    final jsonString = prefs.getString(key);
    if (jsonString == null) return [];

    final List<dynamic> jsonList = json.decode(jsonString);
    return jsonList
        .map((json) => fromMap(json as Map<String, dynamic>))
        .toList();
  }

  Future<void> _saveList<T>(
    String key,
    List<T> items,
    Map<String, dynamic> Function(T) toMap,
  ) async {
    final prefs = await _prefs;
    final jsonList = items.map((item) => toMap(item)).toList();
    await prefs.setString(key, json.encode(jsonList));
  }

  // User operations
  Future<int> insertUser(User user) async {
    final users = await getUsers();
    users.add(user);
    await _saveList(_usersKey, users, (user) => user.toMap());
    return 1; // Success
  }

  Future<List<User>> getUsers() async {
    return await _getList(_usersKey, (json) => User.fromMap(json));
  }

  Future<User?> getUserById(String id) async {
    final users = await getUsers();
    try {
      return users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<List<User>> getUsersByRole(UserRole role) async {
    final users = await getUsers();
    return users.where((user) => user.role == role && user.isActive).toList();
  }

  // Milk product operations
  Future<int> insertMilkProduct(MilkProduct product) async {
    final products = await getMilkProducts();
    products.add(product);
    await _saveList(_productsKey, products, (product) => product.toMap());
    return 1; // Success
  }

  Future<List<MilkProduct>> getMilkProducts() async {
    return await _getList(_productsKey, (json) => MilkProduct.fromMap(json));
  }

  // Order operations
  Future<String> insertOrder(Order order) async {
    final orders = await _getAllOrders();
    orders.add(order);
    await _saveList(_ordersKey, orders, (order) => order.toMap());
    return order.id;
  }

  Future<List<Order>> _getAllOrders() async {
    return await _getList(_ordersKey, (json) => Order.fromMap(json));
  }

  Future<List<Order>> getOrders({
    String? customerId,
    String? fieldManId,
  }) async {
    final allOrders = await _getAllOrders();

    if (customerId != null) {
      return allOrders
          .where((order) => order.customerId == customerId)
          .toList();
    } else if (fieldManId != null) {
      return allOrders
          .where((order) => order.fieldManId == fieldManId)
          .toList();
    }

    return allOrders;
  }

  Future<int> updateOrder(Order order) async {
    final orders = await _getAllOrders();
    final index = orders.indexWhere((o) => o.id == order.id);
    if (index != -1) {
      orders[index] = order;
      await _saveList(_ordersKey, orders, (order) => order.toMap());
      return 1;
    }
    return 0;
  }

  Future<int> deleteOrder(String orderId) async {
    final orders = await _getAllOrders();
    orders.removeWhere((order) => order.id == orderId);
    await _saveList(_ordersKey, orders, (order) => order.toMap());
    return 1;
  }

  // Payment operations
  Future<int> insertPayment(Payment payment) async {
    final payments = await _getAllPayments();
    payments.add(payment);
    await _saveList(_paymentsKey, payments, (payment) => payment.toMap());
    return 1; // Success
  }

  Future<List<Payment>> _getAllPayments() async {
    return await _getList(_paymentsKey, (json) => Payment.fromMap(json));
  }

  Future<List<Payment>> getPayments({
    String? customerId,
    String? orderId,
  }) async {
    final allPayments = await _getAllPayments();

    if (customerId != null) {
      return allPayments
          .where((payment) => payment.customerId == customerId)
          .toList();
    } else if (orderId != null) {
      return allPayments
          .where((payment) => payment.orderId == orderId)
          .toList();
    }

    return allPayments;
  }

  // Notification operations
  Future<int> insertNotification(AppNotification notification) async {
    final notifications = await _getAllNotifications();
    notifications.add(notification);
    await _saveList(
      _notificationsKey,
      notifications,
      (notification) => notification.toMap(),
    );
    return 1; // Success
  }

  Future<List<AppNotification>> _getAllNotifications() async {
    return await _getList(
      _notificationsKey,
      (json) => AppNotification.fromMap(json),
    );
  }

  Future<List<AppNotification>> getNotifications() async {
    return await _getAllNotifications();
  }

  Future<void> updateNotification(AppNotification notification) async {
    final notifications = await _getAllNotifications();
    final index = notifications.indexWhere((n) => n.id == notification.id);
    if (index != -1) {
      notifications[index] = notification;
      await _saveList(
        _notificationsKey,
        notifications,
        (notification) => notification.toMap(),
      );
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    final notifications = await _getAllNotifications();
    notifications.removeWhere((n) => n.id == notificationId);
    await _saveList(
      _notificationsKey,
      notifications,
      (notification) => notification.toMap(),
    );
  }

  // Customer operations
  Future<List<User>> getCustomers() async {
    return await getUsersByRole(UserRole.customer);
  }

  Future<void> close() async {
    // No need to close SharedPreferences
  }

  // Create sample data for testing
  Future<void> _createSampleData() async {
    // Create sample users
    final sampleUsers = [
      User(
        id: 'admin_1',
        name: 'Admin User',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Admin Street',
        role: UserRole.admin,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      User(
        id: 'customer_1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567891',
        address: '456 Customer Lane, City, State 12345',
        role: UserRole.customer,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      User(
        id: 'customer_2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+1234567892',
        address: '789 Dairy Road, Town, State 67890',
        role: UserRole.customer,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      User(
        id: 'fieldman_1',
        name: 'Mike Wilson',
        email: '<EMAIL>',
        phone: '+1234567893',
        address: '321 Field Street',
        role: UserRole.fieldMan,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
    ];

    await _saveList(_usersKey, sampleUsers, (user) => user.toMap());

    // Create sample products
    final sampleProducts = [
      MilkProduct(
        id: 'product_1',
        name: 'Full Cream Milk',
        category: MilkCategory.fullCream,
        pricePerLiter: 45.0,
        fatContent: 3.5,
        description: 'Fresh full cream milk with 3.5% fat content',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      MilkProduct(
        id: 'product_2',
        name: 'Toned Milk',
        category: MilkCategory.toned,
        pricePerLiter: 42.0,
        fatContent: 1.5,
        description: 'Toned milk with 1.5% fat content',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      MilkProduct(
        id: 'product_3',
        name: 'Skimmed Milk',
        category: MilkCategory.skimmed,
        pricePerLiter: 40.0,
        fatContent: 0.1,
        description: 'Fat-free skimmed milk',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
    ];

    await _saveList(_productsKey, sampleProducts, (product) => product.toMap());

    // Create sample orders
    final sampleOrders = [
      Order(
        id: 'order_1',
        customerId: 'customer_1',
        customerName: 'John Doe',
        items: [
          OrderItem(
            productId: 'product_1',
            productName: 'Full Cream Milk',
            productCategory: 'Full Cream',
            quantity: 2.0,
            unitPrice: 45.0,
            totalPrice: 90.0,
          ),
          OrderItem(
            productId: 'product_2',
            productName: 'Toned Milk',
            productCategory: 'Toned',
            quantity: 1.0,
            unitPrice: 42.0,
            totalPrice: 42.0,
          ),
        ],
        totalAmount: 132.0,
        paidAmount: 132.0,
        pendingAmount: 0.0,
        status: OrderStatus.delivered,
        paymentStatus: PaymentStatus.paid,
        orderDate: DateTime.now().subtract(const Duration(days: 5)),
        deliveryDate: DateTime.now().subtract(const Duration(days: 4)),
        fieldManId: 'fieldman_1',
        fieldManName: 'Mike Wilson',
        notes: 'Regular morning delivery',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 4)),
      ),
      Order(
        id: 'order_2',
        customerId: 'customer_2',
        customerName: 'Jane Smith',
        items: [
          OrderItem(
            productId: 'product_3',
            productName: 'Skimmed Milk',
            productCategory: 'Skimmed',
            quantity: 1.5,
            unitPrice: 40.0,
            totalPrice: 60.0,
          ),
        ],
        totalAmount: 60.0,
        paidAmount: 0.0,
        pendingAmount: 60.0,
        status: OrderStatus.pending,
        paymentStatus: PaymentStatus.pending,
        orderDate: DateTime.now().subtract(const Duration(days: 2)),
        deliveryDate: DateTime.now().add(const Duration(days: 1)),
        notes: 'Please deliver before 8 AM',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      Order(
        id: 'order_3',
        customerId: 'customer_1',
        customerName: 'John Doe',
        items: [
          OrderItem(
            productId: 'product_1',
            productName: 'Full Cream Milk',
            productCategory: 'Full Cream',
            quantity: 3.0,
            unitPrice: 45.0,
            totalPrice: 135.0,
          ),
        ],
        totalAmount: 135.0,
        paidAmount: 70.0,
        pendingAmount: 65.0,
        status: OrderStatus.confirmed,
        paymentStatus: PaymentStatus.partiallyPaid,
        orderDate: DateTime.now().subtract(const Duration(days: 1)),
        deliveryDate: DateTime.now(),
        fieldManId: 'fieldman_1',
        fieldManName: 'Mike Wilson',
        notes: 'Partial payment received',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
    ];

    await _saveList(_ordersKey, sampleOrders, (order) => order.toMap());
  }
}
