import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/milk_product.dart';
import '../models/order.dart';
import '../models/payment.dart';
import '../models/notification.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  // Storage keys
  static const String _usersKey = 'users';
  static const String _productsKey = 'products';
  static const String _ordersKey = 'orders';
  static const String _paymentsKey = 'payments';
  static const String _notificationsKey = 'notifications';

  Future<SharedPreferences> get _prefs async =>
      await SharedPreferences.getInstance();

  // Helper methods for JSON serialization
  Future<List<T>> _getList<T>(
    String key,
    T Function(Map<String, dynamic>) fromMap,
  ) async {
    final prefs = await _prefs;
    final jsonString = prefs.getString(key);
    if (jsonString == null) return [];

    final List<dynamic> jsonList = json.decode(jsonString);
    return jsonList
        .map((json) => fromMap(json as Map<String, dynamic>))
        .toList();
  }

  Future<void> _saveList<T>(
    String key,
    List<T> items,
    Map<String, dynamic> Function(T) toMap,
  ) async {
    final prefs = await _prefs;
    final jsonList = items.map((item) => toMap(item)).toList();
    await prefs.setString(key, json.encode(jsonList));
  }

  // User operations
  Future<int> insertUser(User user) async {
    final users = await getUsers();
    users.add(user);
    await _saveList(_usersKey, users, (user) => user.toMap());
    return 1; // Success
  }

  Future<List<User>> getUsers() async {
    return await _getList(_usersKey, (json) => User.fromMap(json));
  }

  Future<User?> getUserById(String id) async {
    final users = await getUsers();
    try {
      return users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<List<User>> getUsersByRole(UserRole role) async {
    final users = await getUsers();
    return users.where((user) => user.role == role && user.isActive).toList();
  }

  // Milk product operations
  Future<int> insertMilkProduct(MilkProduct product) async {
    final products = await getMilkProducts();
    products.add(product);
    await _saveList(_productsKey, products, (product) => product.toMap());
    return 1; // Success
  }

  Future<List<MilkProduct>> getMilkProducts() async {
    return await _getList(_productsKey, (json) => MilkProduct.fromMap(json));
  }

  // Order operations
  Future<String> insertOrder(Order order) async {
    final orders = await _getAllOrders();
    orders.add(order);
    await _saveList(_ordersKey, orders, (order) => order.toMap());
    return order.id;
  }

  Future<List<Order>> _getAllOrders() async {
    return await _getList(_ordersKey, (json) => Order.fromMap(json));
  }

  Future<List<Order>> getOrders({
    String? customerId,
    String? fieldManId,
  }) async {
    final allOrders = await _getAllOrders();

    if (customerId != null) {
      return allOrders
          .where((order) => order.customerId == customerId)
          .toList();
    } else if (fieldManId != null) {
      return allOrders
          .where((order) => order.fieldManId == fieldManId)
          .toList();
    }

    return allOrders;
  }

  // Payment operations
  Future<int> insertPayment(Payment payment) async {
    final payments = await _getAllPayments();
    payments.add(payment);
    await _saveList(_paymentsKey, payments, (payment) => payment.toMap());
    return 1; // Success
  }

  Future<List<Payment>> _getAllPayments() async {
    return await _getList(_paymentsKey, (json) => Payment.fromMap(json));
  }

  Future<List<Payment>> getPayments({
    String? customerId,
    String? orderId,
  }) async {
    final allPayments = await _getAllPayments();

    if (customerId != null) {
      return allPayments
          .where((payment) => payment.customerId == customerId)
          .toList();
    } else if (orderId != null) {
      return allPayments
          .where((payment) => payment.orderId == orderId)
          .toList();
    }

    return allPayments;
  }

  // Notification operations
  Future<int> insertNotification(AppNotification notification) async {
    final notifications = await _getAllNotifications();
    notifications.add(notification);
    await _saveList(
      _notificationsKey,
      notifications,
      (notification) => notification.toMap(),
    );
    return 1; // Success
  }

  Future<List<AppNotification>> _getAllNotifications() async {
    return await _getList(
      _notificationsKey,
      (json) => AppNotification.fromMap(json),
    );
  }

  Future<List<AppNotification>> getNotifications() async {
    return await _getAllNotifications();
  }

  Future<void> updateNotification(AppNotification notification) async {
    final notifications = await _getAllNotifications();
    final index = notifications.indexWhere((n) => n.id == notification.id);
    if (index != -1) {
      notifications[index] = notification;
      await _saveList(
        _notificationsKey,
        notifications,
        (notification) => notification.toMap(),
      );
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    final notifications = await _getAllNotifications();
    notifications.removeWhere((n) => n.id == notificationId);
    await _saveList(
      _notificationsKey,
      notifications,
      (notification) => notification.toMap(),
    );
  }

  // Customer operations
  Future<List<User>> getCustomers() async {
    return await getUsersByRole(UserRole.customer);
  }

  Future<void> close() async {
    // No need to close SharedPreferences
  }
}
