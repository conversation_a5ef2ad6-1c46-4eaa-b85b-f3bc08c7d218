import 'database_service.dart';

class DataInitService {
  static final DataInitService _instance = DataInitService._internal();
  factory DataInitService() => _instance;
  DataInitService._internal();

  final DatabaseService _databaseService = DatabaseService();

  Future<void> initializeSampleData() async {
    try {
      // Use the new database initialization method
      await _databaseService.initializeDatabase();
    } catch (e) {
      print('Error initializing sample data: $e');
    }
  }
}
