import '../models/user.dart';
import '../models/milk_product.dart';
import '../models/order.dart';
import '../models/payment.dart';
import 'database_service.dart';

class DataInitService {
  static final DataInitService _instance = DataInitService._internal();
  factory DataInitService() => _instance;
  DataInitService._internal();

  final DatabaseService _databaseService = DatabaseService();

  Future<void> initializeSampleData() async {
    try {
      // Check if data already exists
      final users = await _databaseService.getUsers();
      if (users.isNotEmpty) {
        return; // Data already initialized
      }

      // Create sample users
      await _createSampleUsers();

      // Create sample milk products
      await _createSampleProducts();

      // Create sample orders
      await _createSampleOrders();

      // Create sample payments
      await _createSamplePayments();
    } catch (e) {
      print('Error initializing sample data: $e');
    }
  }

  Future<void> _createSampleUsers() async {
    final users = [
      User(
        id: 'admin_001',
        name: 'Admin User',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        role: UserRole.admin,
        address: 'Dairy Farm Office, Main Street',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      User(
        id: 'customer_001',
        name: 'Rajesh Kumar',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        role: UserRole.customer,
        address: 'House No. 123, Gandhi Nagar, Delhi',
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      User(
        id: 'customer_002',
        name: 'Priya Sharma',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        role: UserRole.customer,
        address: 'Flat 45, Sector 15, Noida',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      User(
        id: 'field_001',
        name: 'Ramesh Singh',
        email: '<EMAIL>',
        phone: '+91 9876543213',
        role: UserRole.fieldMan,
        address: 'Village Dairy, Sector 12',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
    ];

    for (final user in users) {
      await _databaseService.insertUser(user);
    }
  }

  Future<void> _createSampleProducts() async {
    final products = [
      MilkProduct(
        id: 'product_001',
        name: 'Full Cream Milk',
        category: MilkCategory.fullCream,
        pricePerLiter: 60.0,
        fatContent: 6.0,
        description: 'Rich and creamy full cream milk',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      MilkProduct(
        id: 'product_002',
        name: 'Toned Milk',
        category: MilkCategory.toned,
        pricePerLiter: 50.0,
        fatContent: 3.0,
        description: 'Healthy toned milk with reduced fat',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      MilkProduct(
        id: 'product_003',
        name: 'Double Toned Milk',
        category: MilkCategory.doubleToned,
        pricePerLiter: 45.0,
        fatContent: 1.5,
        description: 'Low fat double toned milk',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      MilkProduct(
        id: 'product_004',
        name: 'Buffalo Milk',
        category: MilkCategory.buffalo,
        pricePerLiter: 70.0,
        fatContent: 7.0,
        description: 'Pure buffalo milk with high fat content',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
    ];

    for (final product in products) {
      await _databaseService.insertMilkProduct(product);
    }
  }

  Future<void> _createSampleOrders() async {
    final orders = [
      Order(
        id: 'order_001',
        customerId: 'customer_001',
        customerName: 'Rajesh Kumar',
        fieldManId: 'field_001',
        fieldManName: 'Ramesh Singh',
        items: [
          OrderItem(
            productId: 'product_001',
            productName: 'Full Cream Milk',
            productCategory: 'fullCream',
            quantity: 2.0,
            unitPrice: 60.0,
            totalPrice: 120.0,
          ),
        ],
        totalAmount: 120.0,
        paidAmount: 120.0,
        pendingAmount: 0.0,
        status: OrderStatus.delivered,
        paymentStatus: PaymentStatus.paid,
        orderDate: DateTime.now().subtract(const Duration(days: 2)),
        deliveryDate: DateTime.now().subtract(const Duration(days: 1)),
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Order(
        id: 'order_002',
        customerId: 'customer_002',
        customerName: 'Priya Sharma',
        fieldManId: 'field_001',
        fieldManName: 'Ramesh Singh',
        items: [
          OrderItem(
            productId: 'product_002',
            productName: 'Toned Milk',
            productCategory: 'toned',
            quantity: 1.0,
            unitPrice: 50.0,
            totalPrice: 50.0,
          ),
          OrderItem(
            productId: 'product_003',
            productName: 'Double Toned Milk',
            productCategory: 'doubleToned',
            quantity: 1.0,
            unitPrice: 45.0,
            totalPrice: 45.0,
          ),
        ],
        totalAmount: 95.0,
        paidAmount: 0.0,
        pendingAmount: 95.0,
        status: OrderStatus.confirmed,
        paymentStatus: PaymentStatus.pending,
        orderDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Order(
        id: 'order_003',
        customerId: 'customer_001',
        customerName: 'Rajesh Kumar',
        items: [
          OrderItem(
            productId: 'product_004',
            productName: 'Buffalo Milk',
            productCategory: 'buffalo',
            quantity: 1.0,
            unitPrice: 70.0,
            totalPrice: 70.0,
          ),
        ],
        totalAmount: 70.0,
        paidAmount: 0.0,
        pendingAmount: 70.0,
        status: OrderStatus.pending,
        paymentStatus: PaymentStatus.pending,
        orderDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    for (final order in orders) {
      await _databaseService.insertOrder(order);
    }
  }

  Future<void> _createSamplePayments() async {
    final payments = [
      Payment(
        id: 'payment_001',
        orderId: 'order_001',
        customerId: 'customer_001',
        customerName: 'Rajesh Kumar',
        fieldManId: 'field_001',
        fieldManName: 'Ramesh Singh',
        amount: 120.0,
        method: PaymentMethod.cash,
        paymentDate: DateTime.now().subtract(const Duration(days: 1)),
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];

    for (final payment in payments) {
      await _databaseService.insertPayment(payment);
    }
  }
}
