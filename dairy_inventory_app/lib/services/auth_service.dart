import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'database_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseService _databaseService = DatabaseService();
  User? _currentUser;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  Future<bool> login(String email, String password) async {
    try {
      // In a real app, you would validate against a secure backend
      // For now, we'll use a simple email-based lookup
      final users = await _databaseService.getUsers();
      final user = users.firstWhere(
        (u) => u.email.toLowerCase() == email.toLowerCase(),
        orElse: () => throw Exception('User not found'),
      );

      if (user.isActive) {
        _currentUser = user;
        await _saveUserSession(user.id);
        return true;
      } else {
        throw Exception('User account is inactive');
      }
    } catch (e) {
      return false;
    }
  }

  Future<bool> register({
    required String name,
    required String email,
    required String phone,
    required UserRole role,
    String? address,
  }) async {
    try {
      final user = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        email: email,
        phone: phone,
        role: role,
        address: address,
        createdAt: DateTime.now(),
      );

      await _databaseService.insertUser(user);
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_id');
  }

  Future<bool> checkAuthStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      
      if (userId != null) {
        final user = await _databaseService.getUserById(userId);
        if (user != null && user.isActive) {
          _currentUser = user;
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> _saveUserSession(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_id', userId);
  }

  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? address,
  }) async {
    if (_currentUser == null) return false;

    try {
      final updatedUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        phone: phone ?? _currentUser!.phone,
        address: address ?? _currentUser!.address,
      );

      // In a real app, you would update this in the database
      _currentUser = updatedUser;
      return true;
    } catch (e) {
      return false;
    }
  }

  bool hasRole(UserRole role) {
    return _currentUser?.role == role;
  }

  bool isAdmin() => hasRole(UserRole.admin);
  bool isCustomer() => hasRole(UserRole.customer);
  bool isFieldMan() => hasRole(UserRole.fieldMan);
}
