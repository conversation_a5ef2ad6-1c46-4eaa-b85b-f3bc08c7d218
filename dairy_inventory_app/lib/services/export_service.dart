import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';

import '../models/order.dart';
import '../models/payment.dart';
import '../models/milk_product.dart';
import '../models/user.dart';

class ExportService {
  static const String _companyName = 'Dairy Fresh';
  static const String _companyAddress =
      '123 Dairy Street, Fresh City, FC 12345';
  static const String _companyPhone = '+****************';
  static const String _companyEmail = '<EMAIL>';

  // PDF Export Methods
  Future<Uint8List> generateOrderReceiptPDF(Order order) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              _buildPDFHeader(),
              pw.SizedBox(height: 30),

              // Receipt Title
              pw.Center(
                child: pw.Text(
                  'ORDER RECEIPT',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.SizedBox(height: 20),

              // Order Details
              _buildOrderDetailsPDF(order),
              pw.SizedBox(height: 20),

              // Items Table
              _buildOrderItemsTablePDF(order.items),
              pw.SizedBox(height: 20),

              // Total Section
              _buildOrderTotalPDF(order),
              pw.SizedBox(height: 30),

              // Footer
              _buildPDFFooter(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  Future<Uint8List> generateSalesReportPDF({
    required List<Order> orders,
    required List<Payment> payments,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return [
            // Header
            _buildPDFHeader(),
            pw.SizedBox(height: 30),

            // Report Title
            pw.Center(
              child: pw.Text(
                'SALES REPORT',
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            pw.SizedBox(height: 10),
            pw.Center(
              child: pw.Text(
                '${_formatDate(startDate)} - ${_formatDate(endDate)}',
                style: const pw.TextStyle(fontSize: 16),
              ),
            ),
            pw.SizedBox(height: 30),

            // Summary Section
            _buildSalesReportSummaryPDF(orders, payments),
            pw.SizedBox(height: 30),

            // Orders Table
            pw.Text(
              'Orders Summary',
              style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            _buildOrdersTablePDF(orders),
            pw.SizedBox(height: 30),

            // Payments Table
            pw.Text(
              'Payments Summary',
              style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            _buildPaymentsTablePDF(payments),
          ];
        },
      ),
    );

    return pdf.save();
  }

  // Excel Export Methods
  Future<String> generateOrdersExcel(List<Order> orders) async {
    final excel = Excel.createExcel();
    final sheet = excel['Orders'];

    // Headers
    final headers = [
      'Order ID',
      'Customer Name',
      'Total Amount',
      'Paid Amount',
      'Pending Amount',
      'Status',
      'Payment Status',
      'Order Date',
      'Delivery Date',
      'Items Count',
      'Notes',
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
    }

    // Data rows
    for (int i = 0; i < orders.length; i++) {
      final order = orders[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(order.id);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(order.customerName);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = DoubleCellValue(order.totalAmount);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = DoubleCellValue(order.paidAmount);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = DoubleCellValue(order.pendingAmount);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = TextCellValue(order.status.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = TextCellValue(order.paymentStatus.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = TextCellValue(_formatDate(order.orderDate));
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
          .value = TextCellValue(
        order.deliveryDate != null ? _formatDate(order.deliveryDate!) : '',
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row))
          .value = IntCellValue(order.items.length);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row))
          .value = TextCellValue(order.notes ?? '');
    }

    return await _saveExcelFile(excel, 'orders_export');
  }

  Future<String> generatePaymentsExcel(List<Payment> payments) async {
    final excel = Excel.createExcel();
    final sheet = excel['Payments'];

    // Headers
    final headers = [
      'Payment ID',
      'Order ID',
      'Customer Name',
      'Amount',
      'Method',
      'Transaction ID',
      'Payment Date',
      'Field Man',
      'Notes',
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
    }

    // Data rows
    for (int i = 0; i < payments.length; i++) {
      final payment = payments[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(payment.id);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(payment.orderId);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue(payment.customerName);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = DoubleCellValue(payment.amount);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = TextCellValue(payment.method.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = TextCellValue(payment.transactionId ?? '');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = TextCellValue(_formatDate(payment.paymentDate));
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = TextCellValue(payment.fieldManName ?? '');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
          .value = TextCellValue(payment.notes ?? '');
    }

    return await _saveExcelFile(excel, 'payments_export');
  }

  Future<String> generateProductsExcel(List<MilkProduct> products) async {
    final excel = Excel.createExcel();
    final sheet = excel['Products'];

    // Headers
    final headers = [
      'Product ID',
      'Name',
      'Category',
      'Price per Liter',
      'Fat Content (%)',
      'Description',
      'Created Date',
      'Active',
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
    }

    // Data rows
    for (int i = 0; i < products.length; i++) {
      final product = products[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(product.id);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(product.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue(product.category.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = DoubleCellValue(product.pricePerLiter);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = DoubleCellValue(product.fatContent);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = TextCellValue(product.description);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = TextCellValue(_formatDate(product.createdAt));
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = TextCellValue(product.isActive ? 'Yes' : 'No');
    }

    return await _saveExcelFile(excel, 'products_export');
  }

  Future<String> generateCustomersExcel(List<User> customers) async {
    final excel = Excel.createExcel();
    final sheet = excel['Customers'];

    // Headers
    final headers = [
      'Customer ID',
      'Name',
      'Email',
      'Phone',
      'Address',
      'Active',
      'Created Date',
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
    }

    // Data rows
    for (int i = 0; i < customers.length; i++) {
      final customer = customers[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(customer.id);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(customer.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue(customer.email);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = TextCellValue(customer.phone);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = TextCellValue(customer.address ?? '');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = TextCellValue(customer.isActive ? 'Yes' : 'No');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = TextCellValue(_formatDate(customer.createdAt));
    }

    return await _saveExcelFile(excel, 'customers_export');
  }

  // Print Methods
  Future<void> printOrderReceipt(Order order) async {
    final pdfData = await generateOrderReceiptPDF(order);
    await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdfData);
  }

  Future<void> printSalesReport({
    required List<Order> orders,
    required List<Payment> payments,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final pdfData = await generateSalesReportPDF(
      orders: orders,
      payments: payments,
      startDate: startDate,
      endDate: endDate,
    );
    await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdfData);
  }

  // Helper Methods
  Future<String> _saveExcelFile(Excel excel, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName.xlsx');
    final bytes = excel.encode();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }
    return file.path;
  }

  pw.Widget _buildPDFHeader() {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              _companyName,
              style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 5),
            pw.Text(_companyAddress),
            pw.Text(_companyPhone),
            pw.Text(_companyEmail),
          ],
        ),
        pw.Container(
          width: 80,
          height: 80,
          decoration: pw.BoxDecoration(border: pw.Border.all()),
          child: pw.Center(
            child: pw.Text(
              'LOGO',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _buildOrderDetailsPDF(Order order) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('Order ID: ${order.id}'),
            pw.Text('Customer: ${order.customerName}'),
            pw.Text('Order Date: ${_formatDate(order.orderDate)}'),
            if (order.deliveryDate != null)
              pw.Text('Delivery Date: ${_formatDate(order.deliveryDate!)}'),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            pw.Text('Status: ${order.status.name.toUpperCase()}'),
            pw.Text('Payment: ${order.paymentStatus.name.toUpperCase()}'),
            if (order.fieldManName != null)
              pw.Text('Field Staff: ${order.fieldManName}'),
          ],
        ),
      ],
    );
  }

  pw.Widget _buildOrderItemsTablePDF(List<OrderItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'Product',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'Quantity',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'Unit Price',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'Total',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
          ],
        ),
        // Data rows
        ...items.map(
          (item) => pw.TableRow(
            children: [
              pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(item.productName),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text('${item.quantity.toStringAsFixed(1)} L'),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text('₹${item.unitPrice.toStringAsFixed(2)}'),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text('₹${item.totalPrice.toStringAsFixed(2)}'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  pw.Widget _buildOrderTotalPDF(Order order) {
    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Container(
        width: 200,
        child: pw.Column(
          children: [
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Total Amount:'),
                pw.Text('₹${order.totalAmount.toStringAsFixed(2)}'),
              ],
            ),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Paid Amount:'),
                pw.Text('₹${order.paidAmount.toStringAsFixed(2)}'),
              ],
            ),
            pw.Divider(),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  'Pending Amount:',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                ),
                pw.Text(
                  '₹${order.pendingAmount.toStringAsFixed(2)}',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  pw.Widget _buildSalesReportSummaryPDF(
    List<Order> orders,
    List<Payment> payments,
  ) {
    final totalOrders = orders.length;
    final totalRevenue = payments.fold(
      0.0,
      (sum, payment) => sum + payment.amount,
    );
    final pendingAmount = orders.fold(
      0.0,
      (sum, order) => sum + order.pendingAmount,
    );

    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        color: PdfColors.grey100,
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: [
          pw.Column(
            children: [
              pw.Text(
                'Total Orders',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text('$totalOrders', style: const pw.TextStyle(fontSize: 18)),
            ],
          ),
          pw.Column(
            children: [
              pw.Text(
                'Total Revenue',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                '₹${totalRevenue.toStringAsFixed(2)}',
                style: const pw.TextStyle(fontSize: 18),
              ),
            ],
          ),
          pw.Column(
            children: [
              pw.Text(
                'Pending Amount',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                '₹${pendingAmount.toStringAsFixed(2)}',
                style: const pw.TextStyle(fontSize: 18),
              ),
            ],
          ),
        ],
      ),
    );
  }

  pw.Widget _buildOrdersTablePDF(List<Order> orders) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(4),
              child: pw.Text(
                'Order ID',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(4),
              child: pw.Text(
                'Customer',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(4),
              child: pw.Text(
                'Amount',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(4),
              child: pw.Text(
                'Status',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
          ],
        ),
        // Data rows (limited to first 20 for space)
        ...orders
            .take(20)
            .map(
              (order) => pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(order.id.substring(0, 8)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(order.customerName),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text('₹${order.totalAmount.toStringAsFixed(2)}'),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(order.status.name),
                  ),
                ],
              ),
            ),
      ],
    );
  }

  pw.Widget _buildPaymentsTablePDF(List<Payment> payments) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(4),
              child: pw.Text(
                'Payment ID',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(4),
              child: pw.Text(
                'Customer',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(4),
              child: pw.Text(
                'Amount',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(4),
              child: pw.Text(
                'Method',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ),
          ],
        ),
        // Data rows (limited to first 20 for space)
        ...payments
            .take(20)
            .map(
              (payment) => pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(payment.id.substring(0, 8)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(payment.customerName),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text('₹${payment.amount.toStringAsFixed(2)}'),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(payment.method.name),
                  ),
                ],
              ),
            ),
      ],
    );
  }

  pw.Widget _buildPDFFooter() {
    return pw.Center(
      child: pw.Column(
        children: [
          pw.Divider(),
          pw.Text(
            'Thank you for your business!',
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'Generated on ${_formatDate(DateTime.now())}',
            style: const pw.TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
