import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/order_provider.dart';
import '../../providers/payment_provider.dart';
import '../../models/order.dart';

class FieldManDashboard extends StatefulWidget {
  const FieldManDashboard({super.key});

  @override
  State<FieldManDashboard> createState() => _FieldManDashboardState();
}

class _FieldManDashboardState extends State<FieldManDashboard> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    final paymentProvider = Provider.of<PaymentProvider>(
      context,
      listen: false,
    );

    final fieldManId = authProvider.currentUser?.id;
    if (fieldManId != null) {
      await Future.wait([
        orderProvider.loadOrders(fieldManId: fieldManId),
        paymentProvider.loadPayments(),
      ]);
    }
  }

  void _logout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();
    if (mounted) {
      Navigator.pushReplacementNamed(context, '/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final user = authProvider.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: Text('Welcome, ${user?.name ?? 'Field Staff'}'),
        actions: [
          IconButton(icon: const Icon(Icons.logout), onPressed: _logout),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: const [
          _FieldManOverview(),
          _DeliveryList(),
          _CashCollection(),
          _DeliveryHistory(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.local_shipping),
            label: 'Deliveries',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.payments),
            label: 'Collection',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.history), label: 'History'),
        ],
      ),
    );
  }
}

class _FieldManOverview extends StatelessWidget {
  const _FieldManOverview();

  @override
  Widget build(BuildContext context) {
    return Consumer2<OrderProvider, PaymentProvider>(
      builder: (context, orderProvider, paymentProvider, child) {
        final todaysOrders = orderProvider.getTodaysOrders();
        final pendingDeliveries = orderProvider.getOrdersByStatus(
          OrderStatus.confirmed,
        );
        final todaysCollection = paymentProvider.getTodaysCollection();
        final cashCollection = paymentProvider.getCashCollectionTotal();

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Today\'s Summary',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // Stats Cards
              Row(
                children: [
                  Expanded(
                    child: _StatCard(
                      title: 'Today\'s Orders',
                      value: '${todaysOrders.length}',
                      icon: Icons.local_shipping,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _StatCard(
                      title: 'Pending',
                      value: '${pendingDeliveries.length}',
                      icon: Icons.pending_actions,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: _StatCard(
                      title: 'Cash Collected',
                      value: '₹${cashCollection.toStringAsFixed(0)}',
                      icon: Icons.payments,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _StatCard(
                      title: 'Total Collection',
                      value: '₹${todaysCollection.toStringAsFixed(0)}',
                      icon: Icons.account_balance_wallet,
                      color: Colors.purple,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Pending Deliveries
              const Text(
                'Pending Deliveries',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              Expanded(
                child:
                    orderProvider.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : pendingDeliveries.isEmpty
                        ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.check_circle_outline,
                                size: 64,
                                color: Colors.green,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'All deliveries completed!',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.green,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Great job today!',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        )
                        : ListView.builder(
                          itemCount: pendingDeliveries.length,
                          itemBuilder: (context, index) {
                            final order = pendingDeliveries[index];
                            return Card(
                              child: ListTile(
                                title: Text(order.customerName),
                                subtitle: Text(
                                  '${order.items.length} items • ₹${order.totalAmount.toStringAsFixed(2)}',
                                ),
                                trailing: ElevatedButton(
                                  onPressed: () {
                                    // TODO: Mark as delivered
                                  },
                                  child: const Text('Deliver'),
                                ),
                              ),
                            );
                          },
                        ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}

class _DeliveryList extends StatelessWidget {
  const _DeliveryList();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Delivery List\n(To be implemented)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}

class _CashCollection extends StatelessWidget {
  const _CashCollection();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Cash Collection\n(To be implemented)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}

class _DeliveryHistory extends StatelessWidget {
  const _DeliveryHistory();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Delivery History\n(To be implemented)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}
