class AppConstants {
  // App Info
  static const String appName = 'Dairy Fresh';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Modern Dairy Inventory Management';
  
  // API Constants
  static const String baseUrl = 'https://api.dairyfresh.com';
  static const int connectTimeout = 30000;
  static const int receiveTimeout = 30000;
  
  // Storage Keys
  static const String userKey = 'user_data';
  static const String tokenKey = 'auth_token';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String onboardingKey = 'onboarding_completed';
  
  // Animation Durations
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration normalAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);
  
  // Spacing
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing40 = 40.0;
  static const double spacing48 = 48.0;
  
  // Border Radius
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  
  // Icon Sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  
  // Font Sizes
  static const double fontSmall = 12.0;
  static const double fontMedium = 14.0;
  static const double fontLarge = 16.0;
  static const double fontXLarge = 18.0;
  static const double fontXXLarge = 20.0;
  static const double fontTitle = 24.0;
  static const double fontHeading = 28.0;
  static const double fontDisplay = 32.0;
  
  // Elevation
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationXHigh = 16.0;
  
  // Demo Credentials
  static const Map<String, String> demoCredentials = {
    '<EMAIL>': 'password123',
    '<EMAIL>': 'password123',
    '<EMAIL>': 'password123',
  };
  
  // Milk Categories
  static const List<String> milkCategories = [
    'Full Cream',
    'Toned',
    'Double Toned',
    'Skimmed',
    'Buffalo',
    'Organic',
  ];
  
  // Order Status
  static const List<String> orderStatuses = [
    'Pending',
    'Confirmed',
    'In Progress',
    'Delivered',
    'Cancelled',
  ];
  
  // Payment Methods
  static const List<String> paymentMethods = [
    'Cash',
    'Online',
    'UPI',
    'Card',
    'Cheque',
  ];
  
  // Dashboard Stats
  static const List<String> adminStats = [
    'Total Orders',
    'Today\'s Collection',
    'Pending Amount',
    'Active Customers',
  ];
  
  static const List<String> customerStats = [
    'Total Orders',
    'Pending Amount',
    'Total Paid',
    'This Month',
  ];
  
  static const List<String> fieldManStats = [
    'Today\'s Orders',
    'Pending Deliveries',
    'Cash Collected',
    'Total Collection',
  ];
  
  // Error Messages
  static const String networkError = 'Network connection failed';
  static const String serverError = 'Server error occurred';
  static const String unknownError = 'Something went wrong';
  static const String validationError = 'Please check your input';
  
  // Success Messages
  static const String loginSuccess = 'Login successful';
  static const String orderCreated = 'Order created successfully';
  static const String orderUpdated = 'Order updated successfully';
  static const String paymentAdded = 'Payment added successfully';
}
