class StringUtils {
  /// Safely truncates a string to a maximum length
  /// Returns the original string if it's shorter than maxLength
  static String safeTruncate(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength);
  }

  /// Formats an ID for display by truncating to 8 characters
  /// Commonly used for order IDs, customer IDs, etc.
  static String formatIdForDisplay(String id) {
    return safeTruncate(id, 8);
  }

  /// Formats an order ID with prefix for display
  static String formatOrderId(String orderId) {
    return '#${formatIdForDisplay(orderId)}';
  }

  /// Formats a customer ID with prefix for display
  static String formatCustomerId(String customerId) {
    return 'Customer ID: ${formatIdForDisplay(customerId)}';
  }

  /// Capitalizes the first letter of a string
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// Converts camelCase or snake_case to Title Case
  static String toTitleCase(String text) {
    return text
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}')
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => capitalize(word))
        .join(' ')
        .trim();
  }

  /// Formats phone number for display
  static String formatPhoneNumber(String phone) {
    // Remove all non-digit characters
    final digits = phone.replaceAll(RegExp(r'\D'), '');
    
    if (digits.length == 10) {
      // Format as (XXX) XXX-XXXX
      return '(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}';
    } else if (digits.length == 11 && digits.startsWith('1')) {
      // Format as +1 (XXX) XXX-XXXX
      return '+1 (${digits.substring(1, 4)}) ${digits.substring(4, 7)}-${digits.substring(7)}';
    }
    
    // Return original if format is not recognized
    return phone;
  }

  /// Validates email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validates phone number format
  static bool isValidPhoneNumber(String phone) {
    final digits = phone.replaceAll(RegExp(r'\D'), '');
    return digits.length >= 10 && digits.length <= 11;
  }

  /// Formats currency amount
  static String formatCurrency(double amount, {String symbol = '₹'}) {
    return '$symbol${amount.toStringAsFixed(2)}';
  }

  /// Formats percentage
  static String formatPercentage(double value, {int decimals = 1}) {
    return '${value.toStringAsFixed(decimals)}%';
  }
}
